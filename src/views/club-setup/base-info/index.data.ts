import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'
import type { DescItem } from '@/components/Description';
import { h } from 'vue';


export const columns: BasicColumn[] = [
  {
    title: '头像',
    dataIndex: 'logoUrl',
    customRender: ({ text }) => {
      return useRender.renderImg(text, 50, { overflow: 'hidden' })
    },
  },
  { title: '名称', dataIndex: 'name' },
  { title: '注册商家名称', dataIndex: 'ownerName' },
  { title: '公众号名称', dataIndex: 'mpPubName' },
  { title: '小程序名称', dataIndex: 'mpAppName' },
  { title: '描述', dataIndex: 'description' },
  { title: '备注', dataIndex: 'remark' },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    customRender: ({ text }) => useRender.renderDate(text),
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '俱乐部名称',
    field: 'name',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '注册商家名称',
    field: 'ownerName',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '公众号名称',
    field: 'mpPubName',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '小程序名称',
    field: 'mpAppName',
    component: 'Input',
    colProps: { span: 8 },
  }
]
export const infoSchema: DescItem[] = [
  {
    label: '头像',
    field: 'logoUrl',
    render: ({ text }) => {
      return useRender.renderImg(text)
    },
    // component: 'FileUpload',
    // componentProps: {
    //   maxCount: 1,
    //   fileType: 'image',
    // },
  },
  {
    field: 'name',
    label: '名称',
    labelMinWidth: 50,
  },
  {
    field: 'ownerId',
    label: '注册商家ID',
    labelMinWidth: 50,
  },
  {
    field: 'ownerName',
    label: '注册商家名称',
    labelMinWidth: 50,
  },
  {
    field: 'entityId',
    label: '绑定的实体ID',
    labelMinWidth: 50,
  },
  {
    field: 'entityName',
    label: '绑定的实体名称',
    labelMinWidth: 50,
  },
  {
    field: 'mpPubName',
    label: '公众号名称',
    labelMinWidth: 50,
  },
  {
    field: 'mpAppName',
    label: '小程序名称',
    labelMinWidth: 50,
  },
  {
    label: '描述',
    field: 'description',
    render: (value) => {
      return h('div', {
        innerHTML: value, // 设置HTML内容
        disabled: true,
        style: {
          maxHeight: '700px',
          overflow: 'auto',
        },
      })
    },
    // component: 'Editor',
  },
  {
    label: '备注',
    field: 'remark',
    render: (value) => {
      return h('div', {
        innerHTML: value, // 设置HTML内容
        disabled: true,
        style: {
          maxHeight: '700px',
          overflow: 'auto',
        },
      })
    },
    // component: 'Editor',
  },
  {
    field: 'createTime',
    label: '创建时间',
    labelMinWidth: 50,
  },

  // {
  //   label: '状态',
  //   field: 'commentVisible',
  //   render: (value) => {
  //     return value === false ? '否' : '是'
  //   },
  // },
]
export interface infoForm {
  id: number
}
