<template>
  <!-- 右侧编辑组件 -->
  <div>
    <div class="configure_page">
      <div class="delete_btn">
        <a-button danger @click="emit('delete')">
          <DeleteOutlined />
          删除当前菜单
        </a-button>
      </div>
      <div>
        <span>菜单名称：</span>
        <a-input class="input_width" v-model:value="menu.name" show-count placeholder="请输入菜单名称"
          :maxlength="isParent ? 4 : 7" clearable />
      </div>
      <div v-if="isLeave">
        <div class="menu_content">
          <span>菜单标识：</span>
          <a-input class="input_width" v-model:value="menu.menuKey" placeholder="请输入菜单 KEY" clearable />
        </div>
        <div class="menu_content">
          <span>菜单内容：</span>
          <a-select v-model:value="menu.type" clearable placeholder="请选择" class="input_width menu_option">
            <a-select-option v-for="item in menuOptions" :value="item.value" :key="item.value">{{ item.label
              }}</a-select-option>
          </a-select>
        </div>
        <div class="more-box">
          <div class="configur_content" v-if="menu.type === 'view'">
            <span>跳转链接：</span>
            <a-input class="input_width" v-model:value="menu.url" placeholder="请输入链接" clearable />
          </div>
          <div class="configur_content" v-if="menu.type === 'miniprogram'">
            <div class="applet">
              <span>小程序的 appid ：</span>
              <a-input class="input_width" v-model:value="menu.miniProgramAppId" placeholder="请输入小程序的appid" clearable />
            </div>
            <div class="applet">
              <span>小程序的页面路径：</span>
              <a-input class="input_width" v-model:value="menu.miniProgramPagePath"
                placeholder="请输入小程序的页面路径，如：pages/index" clearable />
            </div>
            <div class="applet">
              <span>小程序的备用网页：</span>
              <a-input class="input_width" v-model:value="menu.url" placeholder="不支持小程序的老版本客户端将打开本网页" clearable />
            </div>
            <p class="blue">tips:需要和公众号进行关联才可以把小程序绑定带微信菜单上哟！</p>
          </div>
          <div class="configur_content" v-if="menu.type === 'article_view_limited'">
            <a-row>
              <div class="select-item" v-if="menu && menu.replyArticles">
                <WxNews :articles="menu.replyArticles" />
                <a-row class="ope-row" justify="center" align="middle">
                  <a-button danger shape="circle" @click="deleteMaterial">
                    <DeleteOutlined />
                  </a-button>
                </a-row>
              </div>
              <div v-else>
                <a-row justify="center">
                  <a-col :span="24" style="text-align: center">
                    <a-button type="primary" @click="showNewsDialog = true">
                      素材库选择
                      <CheckOutlined />
                    </a-button>
                  </a-col>
                </a-row>
              </div>
              <a-modal title="选择图文" v-model:open="showNewsDialog" width="80%" destroy-on-close>
                <WxMaterialSelect type="news" :account-id="props.accountId" @select-material="selectMaterial" />
              </a-modal>
            </a-row>
          </div>
          <div class="configur_content" v-if="menu.type === 'click' || menu.type === 'scancode_waitmsg'">
            <WxReplySelect v-if="hackResetWxReplySelect" v-model="menu.reply" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import WxReplySelect, { type Reply, ReplyType } from '@/views/mp/components/WxReply'
import WxNews from '@/views/mp/components/WxNews/index.vue'
import WxMaterialSelect from '@/views/mp/components/WxMaterialSelect/index.vue'
import menuOptions from './menuOptions'
// 引入designui图标库
import { DeleteOutlined, CheckOutlined } from '@ant-design/icons-vue'
import { useMessage } from '@/hooks/web/useMessage'
const { createMessage } = useMessage()
/**
 * @param { number } accountId 微信公众号id
 * @param { any } modelValue 左侧选中的具体菜单的绑定值
 * @param { Boolean } isParent 是否是父级菜单的标识
 */
const props = defineProps<{
  accountId: number
  modelValue: any
  isParent: boolean
}>()
const emit = defineEmits<{
  (e: 'delete', v: void)
  (e: 'update:modelValue', v: any)
}>()

const menu = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})
const showNewsDialog = ref(false)
const hackResetWxReplySelect = ref(true)
const isLeave = computed<boolean>(() => !(menu.value.children?.length > 0))

watch(menu, () => {
  hackResetWxReplySelect.value = false // 销毁组件
  nextTick(() => {
    hackResetWxReplySelect.value = true // 重建组件
  })
})

// ======================== 菜单编辑（素材选择） ========================
const selectMaterial = (item: any) => {
  const articleId = item.articleId
  const articles = item.content.newsItem
  // 提示，针对多图文
  if (articles.length > 1) {
    createMessage.warning('您选择的是多图文，将默认跳转第一篇')
  }
  showNewsDialog.value = false

  // 设置菜单的回复
  menu.value.articleId = articleId
  menu.value.replyArticles = []
  articles.forEach((article) => {
    menu.value.replyArticles.push({
      title: article.title,
      description: article.digest,
      picUrl: article.picUrl,
      url: article.url
    })
  })
}

const deleteMaterial = () => {
  delete menu.value['articleId']
  delete menu.value['replyArticles']
}
</script>

<style lang="less" scoped>
.a-input {
  width: 70%;
  margin-right: 2%;
}

.configure_page {
  .delete_btn {
    margin-bottom: 15px;
    text-align: right;
  }

  .menu_content {
    margin-top: 20px;
  }

  .more-box {
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.02) !important;
  }

  .configur_content {
    padding: 20px 10px;
    margin-top: 20px;
    // background-color: #fff;
    border-radius: 5px;

    .select-item {
      width: 280px;
      padding: 10px;
      margin: 0 auto 10px;
      border: 1px solid #eaeaea;

      .ope-row {
        padding-top: 10px;
        text-align: center;
      }
    }
  }

  .blue {
    margin-top: 10px;
    color: #29b6f6;
  }

  .applet {
    margin-bottom: 20px;

    span {
      width: 20%;
    }
  }

  .input_width {
    width: 40%;
  }

  .material {
    .input_width {
      width: 30%;
    }

    .el-textarea {
      width: 80%;
    }
  }
}
</style>
