<script lang="ts" setup>
import { columns, searchFormSchema } from '../track-list/driver-management/driver-management.data'
import DriverForm from '../track-list/driver-management/DriverForm.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'

defineOptions({ name: 'DriverManagement' })
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const [registerTable, { reload }] = useTable({
  title: '车手管理',
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  dataSource: [], // 空数据，显示"暂无相关数据"
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

async function handleSyncTrackDayUsers() {
  try {
    // TODO: 调用同步赛道日报名用户API
    createMessage.success('同步成功')
    reload()
  }
  catch (error) {
    createMessage.error('同步失败')
  }
}

async function handleDelete(record) {
  try {
    // TODO: 调用删除API
    createMessage.success('删除成功')
    reload()
  }
  catch (error) {
    createMessage.error('删除失败')
  }
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-auth="['driver:management:create']" type="primary" :pre-icon="IconEnum.ADD" @click="openModal"> 添加
        </a-button>
        <a-button v-auth="['driver:management:sync']" type="default" style="margin-left: 8px"
          @click="handleSyncTrackDayUsers"> 同步赛道日报名用户 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EYE,
              label: '详情',
              auth: 'driver:management:detail',
              onClick: () => openModal(true, { ...record }),
            },
            {
              icon: IconEnum.DELETE,
              label: '删除',
              auth: 'driver:management:delete',
              popConfirm: {
                title: '确定删除该车手?',
                placement: 'left',
                confirm: () => handleDelete(record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <DriverForm @register="registerModal" @success="reload" />
  </div>
</template>
