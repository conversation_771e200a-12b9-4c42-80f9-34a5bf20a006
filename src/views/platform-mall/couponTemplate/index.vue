<script lang="ts" setup>
import { columns, searchFormSchema } from './couponTemplate.data'
import CouponTemplateForm from '@/views/mall/promotion/coupon/template/CouponTemplateForm.vue'
import { DICT_TYPE } from '@/utils/dict'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { CommonStatusEnum } from '@/enums/systemEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { discountFormat } from '@/views/mall/promotion/coupon/formatter'
import {
  deleteCouponTemplate,
  getCouponTemplatePage,
  updateCouponTemplateStatus,
} from '@/api/mall/promotion/coupon/adminCouponTemplate'
import { DictTag } from '@/components/DictTag'

defineOptions({ name: 'PlatformMallCouponTemplate' })
const { createConfirm, createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const [registerTable, { reload }] = useTable({
  title: '优惠券管理',
  api: getCouponTemplatePage,
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

async function handleStatusChange(row) {
  const status = Number(!row.status)
  const text = status === CommonStatusEnum.ENABLE ? '开启' : '关闭'

  // createConfirm({
  //   iconType: 'warning',
  //   content: `确认要${text}${row.name}优惠劵吗?`,
  //   async onOk() {
  await updateCouponTemplateStatus(row.id, status)
  createMessage.success(`${text}成功`)
  reload()
  //   },
  // })
}

async function handleDelete(record: Recordable) {
  await deleteCouponTemplate(record.id)
  createMessage.success('删除成功')
  reload()
}
</script>

<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-auth="'promotion:coupon-template:admin:create'" type="primary" :pre-icon="IconEnum.ADD"
          @click="openModal">
          新增
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <div v-if="column.key === 'discount'" style="display: flex; justify-content: center">
          <DictTag :type="DICT_TYPE.PROMOTION_DISCOUNT_TYPE" :value="record.discountType" />
          <div class="ml-[5px]">
            {{ discountFormat(record) }}
          </div>
        </div>
        <!-- <template v-if="column.key === 'status'">
          <a-switch :checked="record.status === 0" @change="handleStatusChange(record)" />
        </template> -->
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              label: record.status === 0 ? '关闭' : '开启',
              danger: record.status === 0,
              popConfirm: {
                title: `确认要${record.status === 0 ? '关闭' : '开启'}${record.name}优惠劵吗?`,
                placement: 'left',
                confirm: handleStatusChange.bind(null, record),
              },
            },
            {
              icon: IconEnum.EDIT,
              label: '修改',
              auth: 'promotion:coupon-template:admin:update',
              onClick: () => openModal(true, { ...record, categoryList }),
            },
            {
              icon: IconEnum.DELETE,
              danger: true,
              label: '删除',
              auth: 'promotion:coupon-template:admin:delete',
              popConfirm: {
                title: `是否确认删除优惠劵编号为${record.id}的数据项?`,
                placement: 'left',
                confirm: () => handleDelete(record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <CouponTemplateForm is-platform @register="registerModal" @success="reload" />
  </div>
</template>
