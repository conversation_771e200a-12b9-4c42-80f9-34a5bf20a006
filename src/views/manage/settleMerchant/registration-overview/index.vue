<script lang="ts" setup>
import { ref } from 'vue'
import { columns, searchFormSchema } from './businessList.data'
import BusinessDetail from './BusinessDetail.vue'
import BusinessEditModal from './BusinessEditModal.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { useI18n } from '@/hooks/web/useI18n'
import { getBizOwnerPage, updateBizOwnerStatus } from '@/api/manage/bizOwner'

const { createMessage } = useMessage()
const { t } = useI18n()
const [registerModal, { openModal }] = useModal()

// 页面状态控制
const showDetail = ref(false)
const currentBusinessId = ref(null)
console.log(123)

const [registerTable, { reload }] = useTable({
  title: '商家列表',
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
  },
  api: getBizOwnerPage,
})

// 更新状态
const handleUpdateStatus = async (record, status) => {
  try {
    await updateBizOwnerStatus({ id: record.id, status })
    reload()
    createMessage.success(`更新成功`)
  }
  catch (e) {
    console.error('更新状态失败', e)
    createMessage.error('更新状态失败')
  }
}

// 处理编辑
function handleEdit(record: any) {
  openModal(true, {
    record,
    isUpdate: true,
  })
}

// 处理编辑成功
function handleSuccess() {
  reload()
}
// 处理详情
function handleDetail(record: any) {
  currentBusinessId.value = record.id
  showDetail.value = true
}

// 返回列表
function handleBackToList() {
  showDetail.value = false
  currentBusinessId.value = null
}
</script>

<template>
  <div>
    <!-- 商家列表 -->
    <div v-show="!showDetail">
      <BasicTable @register="registerTable">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="[
              {
                danger: true,
                label: '禁用',
                ifShow: record.status === 0,
                popConfirm: {
                  title: '确定禁用该商家？',
                  placement: 'left',
                  confirm: () => handleUpdateStatus(record, 1),
                },
              },
              {
                label: '启用',
                ifShow: record.status === 1,
                popConfirm: {
                  title: '确定启用该商家？',
                  placement: 'left',
                  confirm: () => handleUpdateStatus(record, 0),
                },
              },
              {
                label: t('action.edit'),
                onClick: () => handleEdit(record),
              },
              {
                label: t('action.detail'),
                onClick: () => handleDetail(record),
              },
            ]" />
          </template>
        </template>
      </BasicTable>
    </div>

    <!-- 商家详情 -->
    <div v-show="showDetail">
      <BusinessDetail :business-id="currentBusinessId" @back-to-list="handleBackToList" />
    </div>

    <!-- 编辑弹窗 -->
    <BusinessEditModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>
