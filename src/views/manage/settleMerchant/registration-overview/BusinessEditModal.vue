<script lang="ts" setup>
import { computed, ref, unref } from 'vue'
import { BasicModal, useModalInner } from '@/components/Modal'
import { BasicForm, useForm } from '@/components/Form'
import { useMessage } from '@/hooks/web/useMessage'

const emit = defineEmits(['success', 'register'])

const { createMessage } = useMessage()

const isUpdate = ref(true)
const rowId = ref('')

const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  labelWidth: 100,
  baseColProps: { span: 24 },
  schemas: [
    {
      label: '注册商家名称',
      field: 'businessName',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入注册商家名称',
      },
    },
    {
      label: '经营简称',
      field: 'businessScope',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入经营简称',
      },
    },
    {
      label: '所在地区',
      field: 'region',
      component: 'Select',
      required: true,
      componentProps: {
        placeholder: '请选择所在地区',
        options: [
          { label: '广东 广州', value: '广东 广州' },
          { label: '天津 天津', value: '天津 天津' },
          { label: '北京 北京', value: '北京 北京' },
          { label: '上海 上海', value: '上海 上海' },
        ],
      },
    },
    {
      label: '手机号',
      field: 'phone',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入手机号',
      },
      rules: [
        {
          pattern: /^1[3-9]\d{9}$/,
          message: '请输入正确的手机号码',
        },
      ],
    },
  ],
  showActionButtonGroup: false,
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields()
  setModalProps({ confirmLoading: false })
  isUpdate.value = !!data?.isUpdate

  if (unref(isUpdate)) {
    rowId.value = data.record.id
    setFieldsValue({
      ...data.record,
    })
  }
})

const getTitle = computed(() => (!unref(isUpdate) ? '新增商家' : '编辑商家'))

async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true })

    // 这里应该调用实际的API接口
    // if (unref(isUpdate)) {
    //   await updateBusiness({ id: rowId.value, ...values })
    // } else {
    //   await createBusiness(values)
    // }

    closeModal()
    emit('success', { isUpdate: unref(isUpdate), values: { ...values, id: rowId.value } })
    createMessage.success('保存成功')
  }
  catch (error) {
    console.error('保存失败', error)
  }
  finally {
    setModalProps({ confirmLoading: false })
  }
}
</script>

<template>
  <BasicModal v-bind="$attrs" :title="getTitle" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
