<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
import { Button, Card, Descriptions, Table, Tabs, Tag } from 'ant-design-vue'
import BusinessEditModal from './BusinessEditModal.vue'
import { PageWrapper } from '@/components/Page'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { getBizOwnerDetail, updateBizOwnerStatus } from '@/api/manage/bizOwner'

defineOptions({ name: 'BusinessDetail' })

// 定义props
const props = defineProps<{
  businessId: string | number | null
}>()

// 定义事件
const emit = defineEmits<{
  backToList: []
}>()

const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
const loading = ref(false)

// 商家数据
const businessData = ref({
  id: null,
  businessName: '',
  businessScope: '',
  region: '',
  phone: '',
  status: 1,
  joinTime: '',
  // 基础信息
  registeredName: '',
  legalPerson: '',
  registrationNumber: '',
  businessLicense: '',
  address: '',
  // 经营主体信息
  operatingEntity: '',
  unifiedSocialCreditCode: '',
  bankAccount: '',
  platformAuditStatus: '',
  paymentPlatformStatus: '',
  createTime: '',
  // 俱乐部信息
  clubName: '',
  clubOwner: '',
  carBrand: '',
  miniProgram: '',
  officialAccount: '',
})

// 获取商家详情
const fetchBusinessDetail = async () => {
  if (!props.businessId)
    return

  try {
    loading.value = true
    const result = await getBizOwnerDetail({ id: props.businessId })
    const data = result.data || {}
 businessData.value = {
      ...businessData.value,
      id: data.id,
      businessName: data.name || '',
      phone: data.contactPhone || '',
      status: data.status,
      joinTime: data.createTime || '',
      registeredName: data.name || '',
      businessScope: data.remark || '',
      region: '未设置',
      legalPerson: data.contactName || '',
      registrationNumber: data.userId || '',
      address: '未设置',
      operatingEntity: data.name || '',
      unifiedSocialCreditCode: data.entityId || '',
      bankAccount: '未设置',
      platformAuditStatus: data.status === 0 ? '启用' : '禁用',
      paymentPlatformStatus: '未设置',
      createTime: data.createTime || '',
      clubName: data.name || '',
      clubOwner: data.contactName || '',
      carBrand: '未设置',
      miniProgram: data.phoneVerified === 0 ? '已验证' : '未验证',
      officialAccount: data.emailVerified === 0 ? '已验证' : '未验证',
    }
  }
  catch (error) {
    console.error('获取商家详情失败:', error)
    createMessage.error('获取商家详情失败')
  }
  finally {
    loading.value = false
  }
}

// 监听businessId变化
watch(
  () => props.businessId,
  (newId) => {
    if (newId)
      fetchBusinessDetail()
  },
  { immediate: true },
)

const activeTab = ref('basic')

// 状态标签颜色
const statusColor = computed(() => {
  return businessData.value.status === 1 ? 'green' : 'red'
})

// 状态文本
const statusText = computed(() => {
  return businessData.value.status === 1 ? '启用' : '禁用'
})

// 返回列表
const goBack = () => {
  emit('backToList')
}

// 切换状态
const toggleStatus = async () => {
  try {
    const newStatus = businessData.value.status === 1 ? 0 : 1
    await updateBizOwnerStatus({ id: businessData.value.id, status: newStatus })
    businessData.value.status = newStatus
    createMessage.success(`已${newStatus === 0 ? '启用' : '禁用'}商家`)
  }
  catch (error) {
    console.error('更新状态失败:', error)
    createMessage.error('更新状态失败')
  }
}

// 修改商家
const editBusiness = () => {
  openModal(true, {
    record: {
      id: businessData.value.id,
      businessName: businessData.value.registeredName || businessData.value.businessName,
      businessScope: businessData.value.businessScope,
      region: businessData.value.region,
      phone: businessData.value.phone,
    },
    isUpdate: true,
  })
}

// 处理编辑成功
const handleEditSuccess = () => {
  fetchBusinessDetail()
  createMessage.success('修改成功')
}

// 基础信息数据
const basicInfoData = computed(() => [
  { label: '商家名称', value: businessData.value.businessName },
  { label: '注册账户ID', value: businessData.value.registrationNumber },
  { label: '联系人', value: businessData.value.legalPerson },
  { label: '联系电话', value: businessData.value.phone },
  { label: '联系邮箱', value: businessData.value.address || '未设置' },
  { label: '电话验证状态', value: businessData.value.miniProgram },
  { label: '邮箱验证状态', value: businessData.value.officialAccount },
  { label: '商家状态', value: statusText.value },
  { label: '创建时间', value: businessData.value.joinTime },
  { label: '备注', value: businessData.value.businessScope || '无' },
])

// 经营主体信息表格列定义
const operatingColumns = [
  { title: '经营主体名称', dataIndex: 'operatingEntity', key: 'operatingEntity' },
  { title: '统一社会信用代码', dataIndex: 'unifiedSocialCreditCode', key: 'unifiedSocialCreditCode' },
  { title: '结算账户', dataIndex: 'bankAccount', key: 'bankAccount' },
  { title: '平台审核状态', dataIndex: 'platformAuditStatus', key: 'platformAuditStatus' },
  { title: '支付平台审核状态', dataIndex: 'paymentPlatformStatus', key: 'paymentPlatformStatus' },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  { title: '操作', key: 'action', width: 100 },
]

// 经营主体信息表格数据
const operatingTableData = computed(() => [
  {
    key: '1',
    operatingEntity: businessData.value.operatingEntity || '广州灵猫数字科技有限公司',
    unifiedSocialCreditCode: businessData.value.unifiedSocialCreditCode || '666',
    bankAccount: businessData.value.bankAccount || '长沙银行 (6288)',
    platformAuditStatus: businessData.value.platformAuditStatus || '审核通过',
    paymentPlatformStatus: businessData.value.paymentPlatformStatus || '审核通过',
    createTime: businessData.value.createTime || '2000-01-01 10:00:00',
  },
  {
    key: '2',
    operatingEntity: '广州灵猫数字科技有限公司',
    unifiedSocialCreditCode: '777',
    bankAccount: '光大银行 (5986)',
    platformAuditStatus: '已驳回',
    paymentPlatformStatus: '审核中',
    createTime: '2000-01-01 10:00:00',
  },
])

// 俱乐部信息表格列定义
const clubColumns = [
  { title: '俱乐部名称', dataIndex: 'clubName', key: 'clubName' },
  { title: '所属经营主体', dataIndex: 'clubOwner', key: 'clubOwner' },
  { title: '汽车品牌', dataIndex: 'carBrand', key: 'carBrand' },
  { title: '小程序', dataIndex: 'miniProgram', key: 'miniProgram' },
  { title: '公众号', dataIndex: 'officialAccount', key: 'officialAccount' },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  { title: '操作', key: 'action', width: 100 },
]

// 俱乐部信息表格数据
const clubTableData = computed(() => [
  {
    key: '1',
    clubName: businessData.value.clubName || '车马会',
    clubOwner: businessData.value.clubOwner || '广州灵猫数字科技有限公司',
    carBrand: businessData.value.carBrand || '宝马',
    miniProgram: businessData.value.miniProgram || '未接收',
    officialAccount: businessData.value.officialAccount || '未接收',
    createTime: businessData.value.createTime || '2000-01-01 10:00:00',
  },
  {
    key: '2',
    clubName: '安斯泰斯',
    clubOwner: '广州灵猫数字科技有限公司',
    carBrand: '安斯泰斯',
    miniProgram: '安斯泰斯小程序',
    officialAccount: '安斯泰斯',
    createTime: '2000-01-01 10:00:00',
  },
])
</script>

<template>
  <PageWrapper :loading="loading">
    <!-- 顶部标题栏 -->
    <div class="business-header">
      <div class="header-left">
        <h2 class="business-title">
          {{ businessData.businessName }}
          <Tag :color="statusColor" class="status-tag">
            {{ statusText }}
          </Tag>
        </h2>
      </div>
      <div class="header-right">
        <Button :type="businessData.status === 1 ? 'danger' : 'primary'" @click="toggleStatus">
          {{ businessData.status === 1 ? '禁用商家' : '启用商家' }}
        </Button>
        <Button type="primary" @click="editBusiness">
          修改
        </Button>
        <Button @click="goBack">
          返回
        </Button>
      </div>
    </div>

    <!-- Tab内容 -->
    <Card class="detail-card">
      <Tabs v-model:active-key="activeTab" type="card">
        <!-- 基础信息 -->
        <Tabs.TabPane key="basic" tab="基础信息">
          <Descriptions :column="2" :label-style="{ width: '150px', fontWeight: 'bold', backgroundColor: '#fafafa' }"
            :content-style="{ paddingLeft: '16px' }" bordered>
            <Descriptions.Item v-for="item in basicInfoData" :key="item.label" :label="item.label">
              {{ item.value }}
            </Descriptions.Item>
          </Descriptions>
        </Tabs.TabPane>

        <!-- 经营主体信息 -->
        <Tabs.TabPane key="operating" tab="经营主体信息">
          <Table :columns="operatingColumns" :data-source="operatingTableData"
            :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }" size="middle">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'platformAuditStatus'">
                <Tag
                  :color="record.platformAuditStatus === '审核通过' ? 'green' : record.platformAuditStatus === '审核中' ? 'orange' : 'red'">
                  {{ record.platformAuditStatus }}
                </Tag>
              </template>
              <template v-else-if="column.key === 'paymentPlatformStatus'">
                <Tag
                  :color="record.paymentPlatformStatus === '审核通过' ? 'green' : record.paymentPlatformStatus === '审核中' ? 'orange' : 'red'">
                  {{ record.paymentPlatformStatus }}
                </Tag>
              </template>
              <template v-else-if="column.key === 'action'">
                <Button type="link" size="small">
                  详情
                </Button>
              </template>
            </template>
          </Table>
        </Tabs.TabPane>

        <!-- 俱乐部信息 -->
        <Tabs.TabPane key="club" tab="俱乐部信息">
          <Table :columns="clubColumns" :data-source="clubTableData"
            :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }" size="middle">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'miniProgram'">
                <Tag :color="record.miniProgram === '未接收' ? 'red' : 'green'">
                  {{ record.miniProgram }}
                </Tag>
              </template>
              <template v-else-if="column.key === 'officialAccount'">
                <Tag :color="record.officialAccount === '未接收' ? 'red' : 'green'">
                  {{ record.officialAccount }}
                </Tag>
              </template>
              <template v-else-if="column.key === 'action'">
                <Button type="link" size="small">
                  详情
                </Button>
              </template>
            </template>
          </Table>
        </Tabs.TabPane>
      </Tabs>
    </Card>

    <!-- 编辑弹窗 -->
    <BusinessEditModal @register="registerModal" @success="handleEditSuccess" />
  </PageWrapper>
</template>

<style lang="less" scoped>
.business-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 3%);

  .header-left {
    .business-title {
      display: flex;
      gap: 12px;
      align-items: center;
      margin: 0;
      font-size: 20px;
      font-weight: 600;

      .status-tag {
        margin: 0;
        font-size: 12px;
      }
    }
  }

  .header-right {
    display: flex;
    gap: 8px;
  }
}

.detail-card {
  :deep(.ant-card-body) {
    padding: 0;
  }

  :deep(.ant-tabs-card .ant-tabs-content) {
    padding: 24px;
  }

  :deep(.ant-tabs-card .ant-tabs-tabpane) {
    background: #fff;
  }
}

:deep(.ant-descriptions-item-label) {
  border-right: 1px solid #f0f0f0;
}

:deep(.ant-descriptions-item-content) {
  word-break: break-all;
}
</style>
