import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

export const columns: BasicColumn[] = [
  { title: '商家名称', dataIndex: 'name', width: 200 },
  { title: '经营简称', dataIndex: 'shortName', width: 150 },
  {
    title: '所在地区',
    dataIndex: 'region',
    width: 120,
    customRender: ({ record }) => {
      const province = record.province || ''
      const city = record.city || ''
      if (province && city)
        return `${province} · ${city}`

      return province || city || ''
    },
  },
  { title: '手机号', dataIndex: 'contactPhone', width: 120 },
  {
    title: '商家状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => useRender.renderDict(text, DICT_TYPE.COMMON_STATUS),
  },
  { title: '入驻时间', dataIndex: 'createTime', width: 160 },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '商家名称',
    field: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入商家名称',
    },
    colProps: { span: 6 },
  },
  {
    label: '商家状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      placeholder: '全部',
      options: getDictOptions(DICT_TYPE.COMMON_STATUS),
    },
    colProps: { span: 6 },
  },
]

export const formSchema: FormSchema[] = [
  { label: '经度', field: 'longitude', show: false, component: 'Input' },
  { label: '纬度', field: 'latitude', show: false, component: 'Input' },
  { label: '省', field: 'province', show: false, component: 'Input' },
  { label: '省code', field: 'provinceCode', show: false, component: 'Input' },
  { label: '市', field: 'city', show: false, component: 'Input' },
  { label: '市code', field: 'cityCode', show: false, component: 'Input' },
  { label: '区', field: 'district', show: false, component: 'Input' },
  { label: '区code', field: 'districtCode', show: false, component: 'Input' },
  { label: 'id', field: 'id', show: false, component: 'Input' },
  { label: '商家名称', field: 'shopName', component: 'Input', required: true, colProps: { span: 12 } },
  { label: '商家类型', field: 'shopType', component: 'Input', required: true, colProps: { span: 12 } },
  {
    label: 'LOGO',
    field: 'logo',
    component: 'FileUpload',
    required: false,
    componentProps: {
      fileType: 'image',
      maxCount: 1,
      tip: '建议尺寸为640x640px，支持jpg/png格式',
    },
    colProps: { span: 12 },
  },

  {
    label: '封面图',
    field: 'cover',
    component: 'FileUpload',
    required: false,
    componentProps: {
      fileType: 'image',
      maxCount: 1,
      tip: '建议尺寸为1000x800px，支持jpg/png格式',
    },
    colProps: { span: 12 },
  },
  {
    label: '商家地址',
    field: 'shopAddress',
    component: 'Input',
    slot: 'shopAddress',
    colProps: { span: 24 },
    required: true,
    rules: [
      {
        required: true,
        validator: (_, value) => {
          if (!value)
            return Promise.reject(new Error('请选择完整的省市区并填写详细地址'))

          return Promise.resolve()
        },
      },
    ],
  },
  { label: '服务标签', field: 'tags', component: 'Input', required: false, colProps: { span: 12 } },
  { label: '营业时间', field: 'openingHours', component: 'Input', required: false, colProps: { span: 12 } },
  { label: '摘要说明', field: 'digest', component: 'InputTextArea', required: false, colProps: { span: 24 } },
  { label: '会员福利', field: 'clubBenefits', component: 'InputTextArea', required: false, colProps: { span: 24 } },
  { label: '商家详情', field: 'detail', component: 'InputTextArea', required: false, colProps: { span: 24 } },
]
