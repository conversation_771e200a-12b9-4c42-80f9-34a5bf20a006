<script lang="ts" setup>
import { columns, searchFormSchema } from './lap-time-grouping.data'
import LapTimeGroupingForm from './LapTimeGroupingForm.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'

defineOptions({ name: 'LapTimeGrouping' })
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

// 模拟数据，与设计图一致
const mockData = [
  {
    id: 1,
    groupName: 'SUV组',
    isPinned: 0,
    publishStatus: 0,
    pinTime: '',
    publishTime: '',
    remarks: '',
    createTime: '2022-12-17 21:39:42',
  },
  {
    id: 2,
    groupName: '体验组',
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-05-17 14:16:18',
    remarks: '',
    createTime: '2019-07-22 01:39:53',
  },
  {
    id: 3,
    groupName: '新能源组',
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2021-03-21 20:19:28',
    remarks: '',
    createTime: '2019-07-22 01:39:53',
  },
  {
    id: 4,
    groupName: '超跑组',
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2021-03-21 20:19:28',
    remarks: '',
    createTime: '2019-07-22 01:39:53',
  },
  {
    id: 5,
    groupName: 'e9x M3 (V8)',
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2021-03-21 20:19:28',
    remarks: '',
    createTime: '2019-07-22 01:39:53',
  },
  {
    id: 6,
    groupName: '女子组',
    isPinned: 0,
    publishStatus: 0,
    pinTime: '',
    publishTime: '',
    remarks: '',
    createTime: '2022-12-17 21:39:42',
  },
  {
    id: 7,
    groupName: '涡轮A/NA组',
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2021-03-21 20:19:28',
    remarks: '',
    createTime: '2019-07-22 01:39:53',
  },
  {
    id: 8,
    groupName: 'M专业组',
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2021-03-21 20:19:28',
    remarks: '',
    createTime: '2019-07-22 01:39:53',
  },
  {
    id: 9,
    groupName: 'M新人组',
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2021-03-21 20:19:28',
    remarks: '',
    createTime: '2019-07-22 01:39:53',
  },
  {
    id: 10,
    groupName: '涡轮组3.0T',
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2021-03-21 20:19:28',
    remarks: '',
    createTime: '2019-07-22 01:39:53',
  },
  {
    id: 11,
    groupName: '涡轮组2.0T',
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2021-03-21 20:19:28',
    remarks: '',
    createTime: '2019-07-22 01:39:53',
  },
  {
    id: 12,
    groupName: '涡轮B 2.0T (新手组)',
    isPinned: 0,
    publishStatus: 0,
    pinTime: '',
    publishTime: '',
    remarks: '',
    createTime: '2022-12-17 21:39:42',
  },
]

const [registerTable, { reload }] = useTable({
  title: '组别管理',
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  dataSource: mockData,
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

async function handlePublish(record) {
  try {
    // TODO: 调用发布API
    createMessage.success('发布成功')
    reload()
  }
  catch (error) {
    createMessage.error('发布失败')
  }
}

async function handleUnpublish(record) {
  try {
    // TODO: 调用取消发布API
    createMessage.success('取消发布成功')
    reload()
  }
  catch (error) {
    createMessage.error('取消发布失败')
  }
}

async function handlePin(record) {
  try {
    // TODO: 调用置顶API
    createMessage.success('置顶成功')
    reload()
  }
  catch (error) {
    createMessage.error('置顶失败')
  }
}

async function handleDelete(record) {
  try {
    // TODO: 调用删除API
    createMessage.success('删除成功')
    reload()
  }
  catch (error) {
    createMessage.error('删除失败')
  }
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-auth="['lap:time:grouping:create']" type="primary" :pre-icon="IconEnum.ADD" @click="openModal"> 添加
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'isPinned'">
          <span :style="{ color: record.isPinned ? '#52c41a' : '#666' }"> {{ record.isPinned ? '已置顶' : '未置顶' }} </span>
        </template>
        <template v-if="column.key === 'publishStatus'">
          <span :style="{ color: record.publishStatus ? '#52c41a' : '#ff4d4f' }"> {{ record.publishStatus ? '已发布' :
            '未发布' }} </span>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EDIT,
              label: '详情',
              auth: 'lap:time:grouping:detail',
              onClick: () => openModal(true, { ...record }),
            },
            ...(record.publishStatus ? [
              {
                icon: IconEnum.CLOSE,
                label: '取消发布',
                auth: 'lap:time:grouping:unpublish',
                color: 'error',
                onClick: () => handleUnpublish(record),
              },
            ] : [
              {
                icon: IconEnum.CHECK,
                label: '立即发布',
                auth: 'lap:time:grouping:publish',
                color: 'success',
                onClick: () => handlePublish(record),
              },
            ]),
            {
              icon: IconEnum.UP,
              label: '置顶',
              auth: 'lap:time:grouping:pin',
              onClick: () => handlePin(record),
            },
            {
              icon: IconEnum.DELETE,
              label: '删除',
              auth: 'lap:time:grouping:delete',
              popConfirm: {
                title: '确定删除该组别?',
                placement: 'left',
                confirm: () => handleDelete(record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <LapTimeGroupingForm @register="registerModal" @success="reload" />
  </div>
</template>
