<script lang="ts" setup>
import { columns, searchFormSchema } from './team-points-record.data'
import TeamPointsRecordForm from './TeamPointsRecordForm.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'

defineOptions({ name: 'TeamPointsRecord' })
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const [registerTable, { reload }] = useTable({
  title: '车队积分记录',
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  dataSource: [], // 空数据，显示"暂无相关数据"
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

async function handleDelete(record) {
  try {
    // TODO: 调用删除API
    createMessage.success('删除成功')
    reload()
  }
  catch (error) {
    createMessage.error('删除失败')
  }
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-auth="['team:points:record:create']" type="primary" :pre-icon="IconEnum.ADD" @click="openModal"> +新增
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EYE,
              label: '详情',
              auth: 'team:points:record:detail',
              onClick: () => openModal(true, { ...record }),
            },
            {
              icon: IconEnum.DELETE,
              label: '删除',
              auth: 'team:points:record:delete',
              popConfirm: {
                title: '确定删除该积分记录?',
                placement: 'left',
                confirm: () => handleDelete(record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <TeamPointsRecordForm @register="registerModal" @success="reload" />
  </div>
</template>
