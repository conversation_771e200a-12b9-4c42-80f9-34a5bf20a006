import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'

export const columns: BasicColumn[] = [
  { title: '车队ID', dataIndex: 'teamId', width: 80 },
  { title: '车队名称', dataIndex: 'teamName', width: 150 },
  { title: '赛事名称', dataIndex: 'eventName', width: 150 },
  { title: '比赛场地', dataIndex: 'venue', width: 150 },
  { title: '分组年度', dataIndex: 'groupYear', width: 100 },
  { title: '年度积分分组', dataIndex: 'annualPointsGroup', width: 120 },
  { title: '奖励流水号', dataIndex: 'rewardSerialNumber', width: 120 },
  { title: '积分值', dataIndex: 'pointsValue', width: 100 },
  { title: '积分摘要', dataIndex: 'pointsSummary', width: 200 },
  { title: '比赛时间', dataIndex: 'competitionTime', width: 160 },
  { title: '创建时间', dataIndex: 'createTime', width: 160 },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '车队名称',
    field: 'teamName',
    component: 'Input',
    componentProps: { placeholder: '请输入车队名称' },
    colProps: { span: 8 },
  },
]

export const formSchema: FormSchema[] = [
  { label: 'ID', field: 'id', component: 'Input', show: false },
  {
    label: '车队', field: 'teamId', component: 'Select', required: true, componentProps: {
      placeholder: '请选择车队',
      options: [
        { label: '风掣FC Racing', value: 1 },
        { label: '中山55racing', value: 2 },
        { label: '师兄车房', value: 3 },
        { label: '堡铧755车队', value: 4 },
        { label: '八狸米', value: 5 },
        { label: 'DF-添宝Motorsport', value: 6 },
        { label: '66车队', value: 7 },
        { label: '沙皇汽车俱乐部', value: 8 },
        { label: 'G2 Racing', value: 9 },
        { label: '中山凌丰Racing', value: 10 },
        { label: '威立賽車俱樂部', value: 11 },
        { label: '深聚力', value: 12 },
      ],
    }
  },
  { label: '赛事名称', field: 'eventName', component: 'Input', required: true },
  { label: '比赛场地', field: 'venue', component: 'Input', required: true },
  { label: '分组年度', field: 'groupYear', component: 'Input', required: true },
  { label: '年度积分分组', field: 'annualPointsGroup', component: 'Input', required: true },
  { label: '奖励流水号', field: 'rewardSerialNumber', component: 'Input', required: true },
  { label: '积分值', field: 'pointsValue', component: 'InputNumber', required: true },
  { label: '积分摘要', field: 'pointsSummary', component: 'InputTextArea', componentProps: { rows: 4 } },
  { label: '比赛时间', field: 'competitionTime', component: 'DatePicker', required: true },
]
