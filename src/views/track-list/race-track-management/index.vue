<script lang="ts" setup>
import { columns, searchFormSchema } from './race-track-management.data'
import RaceTrackForm from './RaceTrackForm.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'

defineOptions({ name: 'RaceTrackManagement' })
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

// 模拟数据，与设计图一致
const mockData = [
  {
    id: 1,
    trackName: '广东国际赛车场',
    abbreviation: 'GIC',
    address: '广东省肇庆市四会市广东国际赛车场',
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2019-07-22 01:38:53',
    remarks: '',
    createTime: '2019-07-22 01:38:53',
  },
  {
    id: 2,
    trackName: '惠州福岗赛车场',
    abbreviation: '福岗赛车场',
    address: '广东省惠州市惠城区福岗赛车场',
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2019-07-22 01:38:26',
    remarks: '',
    createTime: '2019-07-22 01:38:26',
  },
]

const [registerTable, { reload }] = useTable({
  title: '赛车场管理',
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  dataSource: mockData,
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

async function handleUnpublish(record) {
  try {
    // TODO: 调用取消发布API
    createMessage.success('取消发布成功')
    reload()
  }
  catch (error) {
    createMessage.error('取消发布失败')
  }
}

async function handlePin(record) {
  try {
    // TODO: 调用置顶API
    createMessage.success('置顶成功')
    reload()
  }
  catch (error) {
    createMessage.error('置顶失败')
  }
}

async function handleDelete(record) {
  try {
    // TODO: 调用删除API
    createMessage.success('删除成功')
    reload()
  }
  catch (error) {
    createMessage.error('删除失败')
  }
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-auth="['race:track:management:create']" type="primary" :pre-icon="IconEnum.ADD" @click="openModal">
          添加 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'isPinned'">
          <span :style="{ color: record.isPinned ? '#52c41a' : '#666' }"> {{ record.isPinned ? '已置顶' : '未置顶' }} </span>
        </template>
        <template v-if="column.key === 'publishStatus'">
          <span :style="{ color: record.publishStatus ? '#52c41a' : '#666' }"> {{ record.publishStatus ? '已发布' : '未发布'
            }} </span>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EDIT,
              label: '详情',
              auth: 'race:track:management:detail',
              onClick: () => openModal(true, { ...record }),
            },
            {
              icon: IconEnum.CLOSE,
              label: '取消发布',
              auth: 'race:track:management:unpublish',
              color: 'warning',
              onClick: () => handleUnpublish(record),
            },
            {
              icon: IconEnum.UP,
              label: '置顶',
              auth: 'race:track:management:pin',
              onClick: () => handlePin(record),
            },
            {
              icon: IconEnum.DELETE,
              label: '删除',
              auth: 'race:track:management:delete',
              popConfirm: {
                title: '确定删除该赛车场?',
                placement: 'left',
                confirm: () => handleDelete(record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <RaceTrackForm @register="registerModal" @success="reload" />
  </div>
</template>
