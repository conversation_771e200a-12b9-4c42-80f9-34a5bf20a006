import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'

export const columns: BasicColumn[] = [
  { title: '赛车场名称', dataIndex: 'trackName', width: 200 },
  { title: '简称', dataIndex: 'abbreviation', width: 120 },
  { title: '地址', dataIndex: 'address', width: 300 },
  { title: '是否置顶', dataIndex: 'isPinned', width: 100, slots: { customRender: 'isPinned' } },
  { title: '发布状态', dataIndex: 'publishStatus', width: 100, slots: { customRender: 'publishStatus' } },
  { title: '置顶时间', dataIndex: 'pinTime', width: 160 },
  { title: '发布时间', dataIndex: 'publishTime', width: 160 },
  { title: '备注', dataIndex: 'remarks', width: 200 },
  { title: '创建时间', dataIndex: 'createTime', width: 160 },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '赛车场名称',
    field: 'trackName',
    component: 'Input',
    componentProps: { placeholder: '请输入' },
    colProps: { span: 6 },
  },
  {
    label: '是否置顶',
    field: 'isPinned',
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '全部', value: '' },
        { label: '已置顶', value: 1 },
        { label: '未置顶', value: 0 },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '发布状态',
    field: 'publishStatus',
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '全部', value: '' },
        { label: '已发布', value: 1 },
        { label: '未发布', value: 0 },
      ],
    },
    colProps: { span: 6 },
  },
]

export const formSchema: FormSchema[] = [
  { label: 'ID', field: 'id', component: 'Input', show: false },
  { label: '赛车场名称', field: 'trackName', component: 'Input', required: true },
  { label: '简称', field: 'abbreviation', component: 'Input', required: true },
  { label: '地址', field: 'address', component: 'Input', required: true },
  {
    label: '是否置顶', field: 'isPinned', component: 'Select', componentProps: {
      options: [
        { label: '已置顶', value: 1 },
        { label: '未置顶', value: 0 },
      ],
    }
  },
  {
    label: '发布状态', field: 'publishStatus', component: 'Select', componentProps: {
      options: [
        { label: '已发布', value: 1 },
        { label: '未发布', value: 0 },
      ],
    }
  },
  { label: '备注', field: 'remarks', component: 'InputTextArea', componentProps: { rows: 4 } },
]
