import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'

export const columns: BasicColumn[] = [
  { title: '等级ID', dataIndex: 'levelId', width: 100 },
  { title: '等级名称', dataIndex: 'levelName', width: 150 },
  { title: '等级类型', dataIndex: 'levelType', width: 120 },
  { title: '等级', dataIndex: 'level', width: 100 },
  { title: '需达到积分值', dataIndex: 'pointsRequired', width: 120 },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '等级类型',
    field: 'levelType',
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '全部', value: '' },
        { label: '车手', value: 'driver' },
        { label: '车队', value: 'team' },
        { label: '俱乐部', value: 'club' },
      ],
    },
    colProps: { span: 8 },
  },
]

export const formSchema: FormSchema[] = [
  { label: 'ID', field: 'id', component: 'Input', show: false },
  { label: '等级名称', field: 'levelName', component: 'Input', required: true },
  {
    label: '等级类型', field: 'levelType', component: 'Select', required: true, componentProps: {
      placeholder: '请选择等级类型',
      options: [
        { label: '车手', value: 'driver' },
        { label: '车队', value: 'team' },
        { label: '俱乐部', value: 'club' },
      ],
    }
  },
  { label: '等级', field: 'level', component: 'InputNumber', required: true },
  { label: '需达到积分值', field: 'pointsRequired', component: 'InputNumber', required: true },
]
