<script lang="ts" setup>
import { nextTick, ref, unref } from 'vue'
import { formSchema } from './driver-level-settings.data'
import { useMessage } from '@/hooks/web/useMessage'
import { BasicForm, useForm } from '@/components/Form'
import { BasicModal, useModalInner } from '@/components/Modal'

const emit = defineEmits(['success', 'register'])
const { createMessage } = useMessage()
const isUpdate = ref(false)
const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: formSchema,
  showActionButtonGroup: false,
  actionColOptions: { span: 23 },
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields()
  setModalProps({ confirmLoading: false })
  isUpdate.value = !!data?.id
  if (unref(isUpdate)) {
    // TODO: 调用详情API
    setFieldsValue({ ...data })
    await nextTick()
  }
})

async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true })
    // TODO: 调用新增/编辑API
    if (values.id) {
      // await updateDriverLevelSettings(values)
    }
    else {
      // await createDriverLevelSettings(values)
    }

    closeModal()
    emit('success')
    createMessage.success('操作成功')
  }
  finally {
    setModalProps({ confirmLoading: false })
  }
}
</script>
<template>
  <BasicModal v-bind="$attrs" :title="isUpdate ? '编辑等级' : '新增等级'" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
