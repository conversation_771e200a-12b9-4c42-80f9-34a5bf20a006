<script lang="ts" setup>
import { columns, searchFormSchema } from './driver-level-settings.data'
import DriverLevelSettingsForm from './DriverLevelSettingsForm.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'

defineOptions({ name: 'DriverLevelSettings' })
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

// 模拟数据，与设计图一致
const mockData = [
  {
    id: 1,
    levelId: 1,
    levelName: '全速王',
    levelType: '车手',
    level: 1,
    pointsRequired: 100,
  },
]

const [registerTable, { reload }] = useTable({
  title: '项目',
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  dataSource: mockData,
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

async function handleDelete(record) {
  try {
    // TODO: 调用删除API
    createMessage.success('删除成功')
    reload()
  }
  catch (error) {
    createMessage.error('删除失败')
  }
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-auth="['driver:level:settings:create']" type="primary" :pre-icon="IconEnum.ADD" @click="openModal">
          添加 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EDIT,
              label: '详情',
              auth: 'driver:level:settings:detail',
              onClick: () => openModal(true, { ...record }),
            },
            {
              icon: IconEnum.DELETE,
              label: '删除',
              auth: 'driver:level:settings:delete',
              color: 'error',
              popConfirm: {
                title: '确定删除该等级?',
                placement: 'left',
                confirm: () => handleDelete(record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <DriverLevelSettingsForm @register="registerModal" @success="reload" />
  </div>
</template>
