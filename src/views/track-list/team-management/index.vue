<script lang="ts" setup>
import { columns, searchFormSchema } from './team-management.data'
import DriverForm from './TeamForm.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'

defineOptions({ name: 'DriverManagement' })
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

// 模拟API数据
const mockData = [
  { id: 1, name: '风掣FC Racing', photo: '/api/placeholder/100/100', address: '广东省深圳市龙岗区', description: '', createTime: '2023-03-12 20:21:09' },
  { id: 2, name: '中山55racing', photo: '/api/placeholder/100/100', address: '广东省中山市中山火炬开发区', description: '', createTime: '2022-05-02 16:21:30' },
  { id: 3, name: '师兄车房', photo: '/api/placeholder/100/100', address: '广东省深圳市宝安区', description: '', createTime: '2022-04-15 10:30:45' },
  { id: 4, name: '堡铧755车队', photo: '/api/placeholder/100/100', address: '广东省深圳市', description: '', createTime: '2022-03-20 14:22:18' },
  { id: 5, name: '八狸米', photo: '/api/placeholder/100/100', address: '广东省中山市', description: '', createTime: '2022-02-10 09:15:33' },
  { id: 6, name: 'DF-添宝Motorsport', photo: '/api/placeholder/100/100', address: '广东省深圳市', description: '', createTime: '2022-01-25 16:45:12' },
  { id: 7, name: '66车队', photo: '/api/placeholder/100/100', address: '广东省广州市', description: '', createTime: '2021-12-30 11:20:55' },
  { id: 8, name: '沙皇汽车俱乐部', photo: '/api/placeholder/100/100', address: '广东省深圳市', description: '沙皇汽车俱乐部成立于2021年,地处深圳沙井,由一群热...', createTime: '2021-11-15 13:40:28' },
  { id: 9, name: 'G2 Racing', photo: '/api/placeholder/100/100', address: '广东省广州市白云区', description: 'G2 Racing 车队位于广州市白云区,为了让更多的车友,...', createTime: '2021-10-08 15:30:42' },
  { id: 10, name: '中山凌丰Racing', photo: '/api/placeholder/100/100', address: '广东省中山市', description: '', createTime: '2021-09-20 08:55:19' },
  { id: 11, name: '威立賽車俱樂部', photo: '/api/placeholder/100/100', address: '广东省深圳市', description: '', createTime: '2021-08-12 12:25:36' },
  { id: 12, name: '深聚力', photo: '/api/placeholder/100/100', address: '广东省深圳市', description: '', createTime: '2021-07-05 17:10:48' },
]

const [registerTable, { reload }] = useTable({
  title: '车队管理',
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  dataSource: mockData,
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

async function handleDelete(record) {
  try {
    // TODO: 调用删除API
    createMessage.success('删除成功')
    reload()
  }
  catch (error) {
    createMessage.error('删除失败')
  }
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-auth="['driver:management:create']" type="primary" :pre-icon="IconEnum.ADD" @click="openModal"> 添加
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EYE,
              label: '详情',
              auth: 'driver:management:detail',
              onClick: () => openModal(true, { ...record }),
            },
            {
              icon: IconEnum.DELETE,
              label: '删除',
              auth: 'driver:management:delete',
              popConfirm: {
                title: '确定删除该车队?',
                placement: 'left',
                confirm: () => handleDelete(record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <DriverForm @register="registerModal" @success="reload" />
  </div>
</template>
