import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'

export const columns: BasicColumn[] = [
  { title: '车队ID', dataIndex: 'id', width: 80 },
  { title: '车队照片', dataIndex: 'photo', width: 100, customRender: ({ text }) => useRender.renderImg(text) },
  { title: '车队名称', dataIndex: 'name', width: 150 },
  { title: '所在地址', dataIndex: 'address', width: 200 },
  { title: '车队简介', dataIndex: 'description', width: 300 },
  { title: '创建时间', dataIndex: 'createTime', width: 160 },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '车队名称',
    field: 'name',
    component: 'Input',
    componentProps: { placeholder: '请输入' },
    colProps: { span: 8 },
  },
  {
    label: '所在地',
    field: 'location',
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '广东省', value: '广东省' },
        { label: '深圳市', value: '深圳市' },
        { label: '中山市', value: '中山市' },
        { label: '广州市', value: '广州市' },
      ],
    },
    colProps: { span: 8 },
  },
]

export const formSchema: FormSchema[] = [
  { label: 'ID', field: 'id', component: 'Input', show: false },
  { label: '车队名称', field: 'name', component: 'Input', required: true },
  { label: '车队照片', field: 'photo', component: 'Upload', componentProps: { accept: 'image/*' } },
  { label: '所在地址', field: 'address', component: 'Input', required: true },
  { label: '车队简介', field: 'description', component: 'InputTextArea', componentProps: { rows: 4 } },
]
