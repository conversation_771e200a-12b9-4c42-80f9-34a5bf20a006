import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'

export const columns: BasicColumn[] = [
  { title: '车手ID', dataIndex: 'id', width: 80 },
  { title: '车手名称', dataIndex: 'driverName', width: 120 },
  { title: '所在车队名称', dataIndex: 'teamName', width: 150 },
  { title: '微信昵称', dataIndex: 'wechatNickname', width: 120 },
  { title: '手机', dataIndex: 'mobile', width: 120 },
  { title: 'CLUBID', dataIndex: 'clubId', width: 100 },
  { title: '所在地址', dataIndex: 'address', width: 200 },
  { title: '车手简介', dataIndex: 'description', width: 300 },
  { title: '创建时间', dataIndex: 'createTime', width: 160 },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '车队',
    field: 'teamId',
    component: 'Select',
    componentProps: {
      placeholder: '直接选择或搜索',
      showSearch: true,
      filterOption: (input, option) => (option?.label ?? '').includes(input),
      options: [
        { label: '风掣FC Racing', value: 1 },
        { label: '中山55racing', value: 2 },
        { label: '师兄车房', value: 3 },
        { label: '堡铧755车队', value: 4 },
        { label: '八狸米', value: 5 },
        { label: 'DF-添宝Motorsport', value: 6 },
        { label: '66车队', value: 7 },
        { label: '沙皇汽车俱乐部', value: 8 },
        { label: 'G2 Racing', value: 9 },
        { label: '中山凌丰Racing', value: 10 },
        { label: '威立賽車俱樂部', value: 11 },
        { label: '深聚力', value: 12 },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '车手名称',
    field: 'driverName',
    component: 'Input',
    componentProps: { placeholder: '请输入' },
    colProps: { span: 6 },
  },
  {
    label: '用户昵称',
    field: 'userNickname',
    component: 'Input',
    componentProps: { placeholder: '请输入' },
    colProps: { span: 6 },
  },
  {
    label: '手机号',
    field: 'mobile',
    component: 'Input',
    componentProps: { placeholder: '请输入' },
    colProps: { span: 6 },
  },
  {
    label: 'CLUBID',
    field: 'clubId',
    component: 'Input',
    componentProps: { placeholder: '请输入' },
    colProps: { span: 6 },
  },
  {
    label: '所在地',
    field: 'location',
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '广东省', value: '广东省' },
        { label: '深圳市', value: '深圳市' },
        { label: '中山市', value: '中山市' },
        { label: '广州市', value: '广州市' },
        { label: '佛山市', value: '佛山市' },
        { label: '东莞市', value: '东莞市' },
      ],
    },
    colProps: { span: 6 },
  },
]

export const formSchema: FormSchema[] = [
  { label: 'ID', field: 'id', component: 'Input', show: false },
  { label: '车手名称', field: 'driverName', component: 'Input', required: true },
  {
    label: '所在车队', field: 'teamId', component: 'Select', required: true, componentProps: {
      placeholder: '请选择车队',
      options: [
        { label: '风掣FC Racing', value: 1 },
        { label: '中山55racing', value: 2 },
        { label: '师兄车房', value: 3 },
        { label: '堡铧755车队', value: 4 },
        { label: '八狸米', value: 5 },
        { label: 'DF-添宝Motorsport', value: 6 },
        { label: '66车队', value: 7 },
        { label: '沙皇汽车俱乐部', value: 8 },
        { label: 'G2 Racing', value: 9 },
        { label: '中山凌丰Racing', value: 10 },
        { label: '威立賽車俱樂部', value: 11 },
        { label: '深聚力', value: 12 },
      ],
    }
  },
  { label: '微信昵称', field: 'wechatNickname', component: 'Input', required: true },
  { label: '手机号', field: 'mobile', component: 'Input', required: true },
  { label: 'CLUBID', field: 'clubId', component: 'Input', required: true },
  { label: '所在地址', field: 'address', component: 'Input', required: true },
  { label: '车手简介', field: 'description', component: 'InputTextArea', componentProps: { rows: 4 } },
]
