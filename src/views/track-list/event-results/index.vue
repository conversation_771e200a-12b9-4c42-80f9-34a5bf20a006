<script lang="ts" setup>
import { columns, searchFormSchema } from './event-results.data'
import EventResultsForm from './EventResultsForm.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'

defineOptions({ name: 'EventResults' })
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const [registerTable, { reload }] = useTable({
  title: '赛事活动结果',
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  dataSource: [], // 空数据，显示"暂无相关数据"
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

async function handleBatchImport() {
  try {
    // TODO: 调用批量导入API
    createMessage.success('批量导入成功')
    reload()
  }
  catch (error) {
    createMessage.error('批量导入失败')
  }
}

async function handlePublish(record) {
  try {
    // TODO: 调用发布API
    createMessage.success('发布成功')
    reload()
  }
  catch (error) {
    createMessage.error('发布失败')
  }
}

async function handleUnpublish(record) {
  try {
    // TODO: 调用取消发布API
    createMessage.success('取消发布成功')
    reload()
  }
  catch (error) {
    createMessage.error('取消发布失败')
  }
}

async function handleDelete(record) {
  try {
    // TODO: 调用删除API
    createMessage.success('删除成功')
    reload()
  }
  catch (error) {
    createMessage.error('删除失败')
  }
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-auth="['event:results:create']" type="primary" :pre-icon="IconEnum.ADD" @click="openModal"> 添加
        </a-button>
        <a-button v-auth="['event:results:batch:import']" type="default" style="margin-left: 8px"
          @click="handleBatchImport"> 批量导入 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'publishStatus'">
          <span :style="{ color: record.publishStatus ? '#52c41a' : '#666' }"> {{ record.publishStatus ? '已发布' : '未发布'
            }} </span>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EDIT,
              label: '编辑',
              auth: 'event:results:edit',
              onClick: () => openModal(true, { ...record }),
            },
            ...(record.publishStatus ? [
              {
                icon: IconEnum.CLOSE,
                label: '取消发布',
                auth: 'event:results:unpublish',
                color: 'error',
                onClick: () => handleUnpublish(record),
              },
            ] : [
              {
                icon: IconEnum.CHECK,
                label: '发布',
                auth: 'event:results:publish',
                color: 'success',
                onClick: () => handlePublish(record),
              },
            ]),
            {
              icon: IconEnum.DELETE,
              label: '删除',
              auth: 'event:results:delete',
              popConfirm: {
                title: '确定删除该结果?',
                placement: 'left',
                confirm: () => handleDelete(record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <EventResultsForm @register="registerModal" @success="reload" />
  </div>
</template>
