import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'

export const columns: BasicColumn[] = [
  { title: '赛事活动', dataIndex: 'raceEvent', width: 200 },
  { title: '活动组别', dataIndex: 'activityGroup', width: 150 },
  { title: '通用分组', dataIndex: 'generalGroup', width: 150 },
  { title: '昵称', dataIndex: 'nickname', width: 120 },
  { title: '车手姓名', dataIndex: 'driverName', width: 120 },
  { title: '成绩', dataIndex: 'score', width: 100 },
  { title: '车型名称', dataIndex: 'carModelName', width: 150 },
  { title: '发布状态', dataIndex: 'publishStatus', width: 100, slots: { customRender: 'publishStatus' } },
  { title: '发布时间', dataIndex: 'publishTime', width: 160 },
  { title: '创建时间', dataIndex: 'createTime', width: 160 },
  { title: '备注', dataIndex: 'remarks', width: 200 },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '赛事活动',
    field: 'raceEvent',
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '全部', value: '' },
        { label: '圈速王杯第十八届赛道日', value: 'lap_king_18' },
        { label: '粤马会第十七届赛道日', value: 'guangdong_horse_17' },
        { label: '粤马会第十六届赛道日', value: 'guangdong_horse_16' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '活动组别',
    field: 'activityGroup',
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '全部', value: '' },
        { label: 'SUV组', value: 'suv' },
        { label: '体验组', value: 'experience' },
        { label: '新能源组', value: 'new_energy' },
        { label: '超跑组', value: 'supercar' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '通用分组',
    field: 'generalGroup',
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '全部', value: '' },
        { label: '男子组', value: 'male' },
        { label: '女子组', value: 'female' },
        { label: '混合组', value: 'mixed' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '赛车场',
    field: 'raceTrack',
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '全部', value: '' },
        { label: '广东国际赛车场', value: 'gic' },
        { label: '惠州福岗赛车场', value: 'fugang' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '昵称',
    field: 'nickname',
    component: 'Input',
    componentProps: { placeholder: '昵称' },
    colProps: { span: 6 },
  },
  {
    label: '真实姓名',
    field: 'realName',
    component: 'Input',
    componentProps: { placeholder: '真实姓名' },
    colProps: { span: 6 },
  },
  {
    label: '车型名称',
    field: 'carModelName',
    component: 'Input',
    componentProps: { placeholder: '车型名称' },
    colProps: { span: 6 },
  },
  {
    label: '发布状态',
    field: 'publishStatus',
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '全部', value: '' },
        { label: '已发布', value: 1 },
        { label: '未发布', value: 0 },
      ],
    },
    colProps: { span: 6 },
  },
]

export const formSchema: FormSchema[] = [
  { label: 'ID', field: 'id', component: 'Input', show: false },
  {
    label: '赛事活动', field: 'raceEvent', component: 'Select', required: true, componentProps: {
      placeholder: '请选择赛事活动',
      options: [
        { label: '圈速王杯第十八届赛道日', value: 'lap_king_18' },
        { label: '粤马会第十七届赛道日', value: 'guangdong_horse_17' },
        { label: '粤马会第十六届赛道日', value: 'guangdong_horse_16' },
      ],
    }
  },
  {
    label: '活动组别', field: 'activityGroup', component: 'Select', required: true, componentProps: {
      placeholder: '请选择活动组别',
      options: [
        { label: 'SUV组', value: 'suv' },
        { label: '体验组', value: 'experience' },
        { label: '新能源组', value: 'new_energy' },
        { label: '超跑组', value: 'supercar' },
      ],
    }
  },
  {
    label: '通用分组', field: 'generalGroup', component: 'Select', required: true, componentProps: {
      placeholder: '请选择通用分组',
      options: [
        { label: '男子组', value: 'male' },
        { label: '女子组', value: 'female' },
        { label: '混合组', value: 'mixed' },
      ],
    }
  },
  { label: '昵称', field: 'nickname', component: 'Input', required: true },
  { label: '车手姓名', field: 'driverName', component: 'Input', required: true },
  { label: '成绩', field: 'score', component: 'Input', required: true },
  { label: '车型名称', field: 'carModelName', component: 'Input', required: true },
  {
    label: '发布状态', field: 'publishStatus', component: 'Select', componentProps: {
      options: [
        { label: '已发布', value: 1 },
        { label: '未发布', value: 0 },
      ],
    }
  },
  { label: '备注', field: 'remarks', component: 'InputTextArea', componentProps: { rows: 4 } },
]
