<script lang="ts" setup>
import { columns, searchFormSchema } from './event-activities.data'
import EventActivitiesForm from './EventActivitiesForm.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'

defineOptions({ name: 'EventActivities' })
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

// 模拟数据，与设计图一致
const mockData = [
  {
    id: 1,
    activityName: '圈速王杯第十八届赛道日 (2023年5月7日)',
    activityTime: '2023-05-07',
    participants: 110,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2023-05-09 22:48:28',
    remarks: '',
    createTime: '2023-05-14 12:46:06',
  },
  {
    id: 2,
    activityName: '粤马会第十七届赛道日 (2023年3月12日)',
    activityTime: '2023-03-12',
    participants: 111,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2023-03-12 11:43:44',
    remarks: '',
    createTime: '2023-03-12 11:41:24',
  },
  {
    id: 3,
    activityName: '粤马会第十六届赛道日 (2022年12月18日)',
    activityTime: '2022-12-18',
    participants: 100,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-12-18 10:00:00',
    remarks: '',
    createTime: '2022-12-18 09:00:00',
  },
  {
    id: 4,
    activityName: '粤马会第十五届赛道日 (2022年11月20日)',
    activityTime: '2022-11-20',
    participants: 104,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-11-20 10:00:00',
    remarks: '',
    createTime: '2022-11-20 09:00:00',
  },
  {
    id: 5,
    activityName: '粤马会第十四届赛道日 (2022年10月16日)',
    activityTime: '2022-10-16',
    participants: 98,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-10-16 10:00:00',
    remarks: '',
    createTime: '2022-10-16 09:00:00',
  },
  {
    id: 6,
    activityName: '粤马会第十三届赛道日 (2022年9月18日)',
    activityTime: '2022-09-18',
    participants: 105,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-09-18 10:00:00',
    remarks: '',
    createTime: '2022-09-18 09:00:00',
  },
  {
    id: 7,
    activityName: '粤马会第十二届赛道日 (2022年8月21日)',
    activityTime: '2022-08-21',
    participants: 102,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-08-21 10:00:00',
    remarks: '',
    createTime: '2022-08-21 09:00:00',
  },
  {
    id: 8,
    activityName: '粤马会第十一届赛道日 (2022年7月17日)',
    activityTime: '2022-07-17',
    participants: 108,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-07-17 10:00:00',
    remarks: '',
    createTime: '2022-07-17 09:00:00',
  },
  {
    id: 9,
    activityName: '粤马会第十届赛道日 (2022年6月19日)',
    activityTime: '2022-06-19',
    participants: 95,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-06-19 10:00:00',
    remarks: '',
    createTime: '2022-06-19 09:00:00',
  },
  {
    id: 10,
    activityName: '粤马会第九届赛道日 (2022年5月15日)',
    activityTime: '2022-05-15',
    participants: 103,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-05-15 10:00:00',
    remarks: '',
    createTime: '2022-05-15 09:00:00',
  },
  {
    id: 11,
    activityName: '粤马会第八届赛道日 (2022年4月17日)',
    activityTime: '2022-04-17',
    participants: 97,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-04-17 10:00:00',
    remarks: '',
    createTime: '2022-04-17 09:00:00',
  },
  {
    id: 12,
    activityName: '粤马会第七届赛道日 (2022年3月20日)',
    activityTime: '2022-03-20',
    participants: 101,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-03-20 10:00:00',
    remarks: '',
    createTime: '2022-03-20 09:00:00',
  },
  {
    id: 13,
    activityName: '粤马会第六届赛道日 (2022年2月20日)',
    activityTime: '2022-02-20',
    participants: 99,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-02-20 10:00:00',
    remarks: '',
    createTime: '2022-02-20 09:00:00',
  },
  {
    id: 14,
    activityName: '粤马会第五届赛道日 (2022年1月16日)',
    activityTime: '2022-01-16',
    participants: 106,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2022-01-16 10:00:00',
    remarks: '',
    createTime: '2022-01-16 09:00:00',
  },
  {
    id: 15,
    activityName: '粤马会第四届赛道日 (2019年12月15日)',
    activityTime: '2019-12-15',
    participants: 93,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2019-12-15 10:00:00',
    remarks: '',
    createTime: '2019-12-15 09:00:00',
  },
  {
    id: 16,
    activityName: '粤马会第三届赛道日 (2018年11月18日)',
    activityTime: '2018-11-18',
    participants: 89,
    isPinned: 0,
    publishStatus: 1,
    pinTime: '',
    publishTime: '2018-11-18 10:00:00',
    remarks: '',
    createTime: '2018-11-18 09:00:00',
  },
]

const [registerTable, { reload }] = useTable({
  title: '活动管理',
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  dataSource: mockData,
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

async function handleActivityGroup(record) {
  try {
    // TODO: 跳转到活动组别页面
    createMessage.success('跳转到活动组别页面')
  }
  catch (error) {
    createMessage.error('跳转失败')
  }
}

async function handleUnpublish(record) {
  try {
    // TODO: 调用取消发布API
    createMessage.success('取消发布成功')
    reload()
  }
  catch (error) {
    createMessage.error('取消发布失败')
  }
}

async function handlePin(record) {
  try {
    // TODO: 调用置顶API
    createMessage.success('置顶成功')
    reload()
  }
  catch (error) {
    createMessage.error('置顶失败')
  }
}

async function handleDelete(record) {
  try {
    // TODO: 调用删除API
    createMessage.success('删除成功')
    reload()
  }
  catch (error) {
    createMessage.error('删除失败')
  }
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-auth="['event:activities:create']" type="primary" :pre-icon="IconEnum.ADD" @click="openModal"> 添加
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'isPinned'">
          <span :style="{ color: record.isPinned ? '#52c41a' : '#666' }"> {{ record.isPinned ? '已置顶' : '未置顶' }} </span>
        </template>
        <template v-if="column.key === 'publishStatus'">
          <span :style="{ color: record.publishStatus ? '#52c41a' : '#666' }"> {{ record.publishStatus ? '已发布' : '未发布'
            }} </span>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EYE,
              label: '详情',
              auth: 'event:activities:detail',
              onClick: () => openModal(true, { ...record }),
            },
            ...(record.id <= 8 ? [
              {
                icon: IconEnum.TEAM,
                label: '活动组别',
                auth: 'event:activities:group',
                onClick: () => handleActivityGroup(record),
              },
            ] : []),
            {
              icon: IconEnum.CLOSE,
              label: '取消发布',
              auth: 'event:activities:unpublish',
              color: 'error',
              onClick: () => handleUnpublish(record),
            },
            {
              icon: IconEnum.UP,
              label: '置顶',
              auth: 'event:activities:pin',
              onClick: () => handlePin(record),
            },
            {
              icon: IconEnum.DELETE,
              label: '删除',
              auth: 'event:activities:delete',
              popConfirm: {
                title: '确定删除该活动?',
                placement: 'left',
                confirm: () => handleDelete(record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <EventActivitiesForm @register="registerModal" @success="reload" />
  </div>
</template>
