import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'

export const columns: BasicColumn[] = [
  { title: '分组年度', dataIndex: 'groupYear', width: 100 },
  { title: '组别名称', dataIndex: 'groupName', width: 200 },
  { title: '备注', dataIndex: 'remarks', width: 200 },
  { title: '创建时间', dataIndex: 'createTime', width: 160 },
  { title: '更新时间', dataIndex: 'updateTime', width: 160 },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '分组名称',
    field: 'groupName',
    component: 'Input',
    componentProps: { placeholder: '分组名称' },
    colProps: { span: 8 },
  },
]

export const formSchema: FormSchema[] = [
  { label: 'ID', field: 'id', component: 'Input', show: false },
  { label: '分组年度', field: 'groupYear', component: 'Input', required: true },
  { label: '组别名称', field: 'groupName', component: 'Input', required: true },
  { label: '备注', field: 'remarks', component: 'InputTextArea', componentProps: { rows: 4 } },
]
