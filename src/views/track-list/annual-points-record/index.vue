<script lang="ts" setup>
import { columns, searchFormSchema } from './annual-points-record.data'
import AnnualPointsRecordForm from './AnnualPointsRecordForm.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'

defineOptions({ name: 'AnnualPointsRecord' })
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

// 模拟数据，与设计图一致
const mockData = [
  {
    id: 1,
    groupYear: '2023',
    groupName: '新能源组',
    remarks: '',
    createTime: '2023-12-18 10:00:23',
    updateTime: '',
  },
  {
    id: 2,
    groupYear: '2023',
    groupName: '高性能超跑专业组',
    remarks: '',
    createTime: '2023-12-18 09:59:57',
    updateTime: '',
  },
  {
    id: 3,
    groupYear: '2023',
    groupName: 'M-power专业组',
    remarks: '',
    createTime: '2023-12-17 16:51:29',
    updateTime: '2023-12-18 10:00:09',
  },
]

const [registerTable, { reload }] = useTable({
  title: '分组管理',
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  dataSource: mockData,
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})

async function handleDelete(record) {
  try {
    // TODO: 调用删除API
    createMessage.success('删除成功')
    reload()
  }
  catch (error) {
    createMessage.error('删除失败')
  }
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-auth="['annual:points:record:create']" type="primary" :pre-icon="IconEnum.ADD" @click="openModal">
          添加 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EDIT,
              label: '详情',
              auth: 'annual:points:record:detail',
              onClick: () => openModal(true, { ...record }),
            },
            {
              icon: IconEnum.DELETE,
              label: '删除',
              auth: 'annual:points:record:delete',
              popConfirm: {
                title: '确定删除该分组?',
                placement: 'left',
                confirm: () => handleDelete(record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <AnnualPointsRecordForm @register="registerModal" @success="reload" />
  </div>
</template>
