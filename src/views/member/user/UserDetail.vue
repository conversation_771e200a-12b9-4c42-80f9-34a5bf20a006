<script lang="ts" setup>
import { ref } from 'vue'
import { infoSchema } from './user.data'
import { BasicModal, useModalInner } from '@/components/Modal'
import { Description, useDescription } from '@/components/Description/index'
import { getUser } from '@/api/member/user'

defineOptions({ name: 'OperLogInfoModal' })

const logData = ref()
const [registerModalInner, { closeModal }] = useModalInner(async (record: Recordable) => {
  logData.value = await getUser(record.id)
})

const [registerDescription] = useDescription({
  column: 2,
  schema: infoSchema,
  data: logData,
})
</script>

<template>
  <BasicModal v-bind="$attrs" title="终端会员详情" width="900px" @register="registerModalInner" @ok="closeModal">
    <Description @register="registerDescription" />
  </BasicModal>
</template>
