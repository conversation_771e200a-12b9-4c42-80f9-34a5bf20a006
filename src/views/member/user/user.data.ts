import { getSimpleGroupList } from '@/api/member/group'
import { getSimpleLevelList } from '@/api/member/level'
import { getSimpleTagList } from '@/api/member/tag'
import { getAreaTree, getRegion } from '@/api/system/area'
import type { DescItem } from '@/components/Description'
import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

export const columns: BasicColumn[] = [
  {
    title: 'CLUB_ID',
    dataIndex: 'id',
    width: 100,
  },
  {
    title: 'openid',
    dataIndex: 'openid',
    width: 100,
  },
  {
    title: 'unionid',
    dataIndex: 'unionid',
    width: 100,
  },
  {
    title: '头像',
    dataIndex: 'avatar',
    width: 80,
    customRender: ({ text }) => {
      return useRender.renderImg(text)
    },
  },
  {
    title: '昵称',
    dataIndex: 'nickname',
  },
  {
    title: '姓名',
    dataIndex: 'name',
    width: 100,
  },
  {
    title: '微信',
    dataIndex: 'wechat',
    width: 100,
  },
  {
    title: '手机号',
    dataIndex: 'mobile',
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 100,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.SYSTEM_USER_SEX)
    },
  },
  {
    title: '常住地址',
    dataIndex: 'liveAddress',
    width: 100,
  },
  {
    title: '所在群',
    dataIndex: 'groupName',
    width: 100,
  },
  {
    title: '积分',
    dataIndex: 'point',
    width: 100,
  },
  {
    title: '车型',
    dataIndex: 'carType',
    width: 100,
  },
  {
    title: '车牌',
    dataIndex: 'carNumber',
    width: 100,
  },
  {
    title: '注册来源',
    dataIndex: 'registerSource',
    width: 100,
  },
  {
    title: '认证状态',
    dataIndex: 'verifyStatus',
    width: 100,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.MEMBER_AUTH_STATUS)
    },
  },
  {
    title: '入群状态',
    dataIndex: 'groupStatus',
    width: 100,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.MEMBER_JOIN_STATUS)
    },
  },
  {
    title: '管理员角色',
    dataIndex: 'clubRoleCodes',
    width: 100,
  },
  {
    title: '服务区',
    dataIndex: 'serviceArea',
    width: 100,
  },
  {
    title: '账号状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.MEMBER_STATUS)
    },
  },
  {
    title: '注册时间',
    dataIndex: 'createTime',
    width: 160,
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '关键字搜索',
    field: 'keyword',
    component: 'Input',
    componentProps: {
      placeholder: 'OPENID/ClubID/昵称/真实姓名/手机号码/微信号/群昵称',
    },
    colProps: { span: 8 },
  },
  {
    label: '常住地区',
    field: 'region',
    component: 'Cascader',
    colProps: { span: 8 },
  },
  {
    label: '认证状态',
    field: 'authStatus',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: getDictOptions(DICT_TYPE.MEMBER_AUTH_STATUS),
    },
  },
  {
    label: '入群状态',
    field: 'joinStatus',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: getDictOptions(DICT_TYPE.MEMBER_JOIN_STATUS),
    },
  },
  {
    label: '管理员角色',
    field: 'adminRole',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      // api: () => getSimpleAdminRoleList(),
    },
  },
  {
    label: '性别',
    field: 'sex',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: getDictOptions(DICT_TYPE.SYSTEM_USER_SEX),
    },
  },
  {
    label: '账号状态',
    field: 'status',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: getDictOptions(DICT_TYPE.MEMBER_STATUS),
    },
  },
  {
    label: '服务区',
    field: 'serviceArea',
    component: 'Select',
    colProps: { span: 8 },
  },
  {
    label: '注册来源',
    field: 'registerSource',
    component: 'Select',
    colProps: { span: 8 },
  },
]

export const formSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input',
  },
  {
    label: '真实姓名',
    field: 'name',
    readonly: true,
    component: 'Input',
  },
  {
    label: '手机号码',
    field: 'mobile',
    readonly: true,
    component: 'Input',
  },
  {
    label: '微信号',
    field: 'wechat',
    readonly: true,
    component: 'Input',
  },
  {
    label: '所在地区',
    field: 'areaName',
    readonly: true,
    component: '',
    componentProps: {
      api: async () => {
        const res = await getRegion()
        return handleTreeFn(res, 'code', 'parentCode', 'children')
      },
      handleTree: 'id',
      fieldNames: {
        label: 'name',
        value: 'id',
        children: 'children',
      },
    },
  },
  {
    label: '车主姓名',
    field: 'carOwner',
    readonly: true,
    component: 'Input',
  },
  {
    label: '是否本人名下',
    field: 'isOwner',
    readonly: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.CAR_IS_OWNER),
    },
  },
  {
    label: '车品牌',
    field: 'carBrand',
    readonly: true,
    component: 'Input',
  },
  {
    label: '车系',
    field: 'carTrain',
    readonly: true,
    component: 'Input',
  },
  {
    label: '车型',
    field: 'carType',
    readonly: true,
    component: 'Input',
  },
  {
    label: '颜色',
    field: 'carColor',
    readonly: true,
    component: 'Input',
  },
  {
    label: '车牌',
    field: 'carNumber',
    readonly: true,
    component: 'Input',
  },
  {
    label: '整车照片',
    field: 'carphoto',
    readonly: true,
    component: 'FileUpload',
    componentProps: {
      maxCount: 1,
      fileType: 'image',
    },
  },
  {
    label: '行驶证',
    field: 'drivingLicence',
    readonly: true,
    component: 'FileUpload',
    componentProps: {
      maxCount: 1,
      fileType: 'image',
    },
  },
  {
    label: '公司名称',
    field: 'companyName',
    readonly: true,
    component: 'Input',
  },
  {
    label: '所在行业',
    field: 'industry',
    readonly: true,
    component: 'Input',
  },
  {
    label: '职位',
    field: 'job',
    readonly: true,
    component: 'Input',
  },
  {
    label: '附加信息',
    field: 'extraInfo',
    readonly: true,
    component: 'InputTextArea',
  },
  {
    label: '驾驶证',
    field: 'drivingLicence',
    readonly: true,
    component: 'FileUpload',
    componentProps: {
      maxCount: 1,
      fileType: 'image',
    },
  },
  {
    label: '行驶证注册日期',
    field: 'registerDate',
    readonly: true,
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD ',
      valueFormat: 'YYYY-MM-DD ',
    },
  },
  {
    label: '行驶证发证日期',
    field: 'issueDate',
    readonly: true,
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD ',
      valueFormat: 'YYYY-MM-DD ',
    },
  },
]

export const updateLevelFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input',
  },
  {
    label: '会员昵称',
    field: 'nickname',
    dynamicDisabled: true,
    component: 'Input',
  },
  {
    label: '会员等级',
    field: 'levelId',
    component: 'ApiSelect',
    componentProps: {
      api: () => getSimpleLevelList(),
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    label: '修改原因',
    field: 'reason',
    required: true,
    component: 'InputTextArea',
  },
]

export const userBasicInfoDesc: DescItem[] = [
  {
    label: '会员名',
    field: 'name',
  },
  {
    label: '昵称',
    field: 'nickname',
  },
  {
    label: '手机号',
    field: 'mobile',
  },
  {
    label: '性别',
    field: 'sex',
    render: (curVal) => {
      return useRender.renderDict(curVal, DICT_TYPE.SYSTEM_USER_SEX)
    },
  },
  {
    label: '所在地',
    field: 'areaName',
  },
  {
    label: '注册 IP',
    field: 'registerIp',
  },
  {
    label: '生日',
    field: 'birthday',
    render: (curVal) => {
      return useRender.renderDate(curVal)
    },
  },
  {
    label: '注册时间',
    field: 'createTime',
    render: (curVal) => {
      return useRender.renderDate(curVal)
    },
  },
  {
    label: '最后登录时间',
    field: 'loginDate',
    render: (curVal) => {
      return useRender.renderDate(curVal)
    },
  },
]

export const userAccountInfoDesc: DescItem[] = [
  {
    label: '等级',
    field: 'levelName',
  },
  {
    label: '成长值',
    field: 'experience',
    render: (curVal) => {
      return curVal || 0
    },
  },
  {
    label: '当前积分',
    field: 'point',
    render: (curVal) => {
      return curVal || 0
    },
  },
  {
    label: '总积分',
    field: 'totalPoint',
    render: (curVal) => {
      return curVal || 0
    },
  },
  {
    label: '当前余额',
    field: 'aaa',
    render: (curVal) => {
      return curVal || 0
    },
  },
  {
    label: '支出金额',
    field: 'bbb',
    render: (curVal) => {
      return curVal || 0
    },
  },
  {
    label: '充值金额',
    field: 'ccc',
    render: (curVal) => {
      return curVal || 0
    },
  },
]

export const infoSchema: DescItem[] = [
  {
    field: 'avatar',
    label: '会员头像',
    render: (curVal) => {
      return useRender.renderImg(curVal)
    },
  },
  {
    field: 'wechat',
    label: '微信昵称',
  },
  {
    field: 'mobile',
    label: '手机号',
  },
  {
    field: 'wechat',
    label: '微信号',
  },
  {
    field: 'groupName',
    label: '群昵称',
  },
  {
    field: 'sex',
    label: '性别',
    render: (curVal) => {
      return useRender.renderDict(curVal, DICT_TYPE.SYSTEM_USER_SEX)
    },
  },
  {
    field: 'birthday',
    label: '生日',
    render: (curVal) => {
      return useRender.renderDate(curVal)
    },
  },
  {
    field: 'areaName',
    label: '所在地区',

  },
  {
    field: 'liveAddress',
    label: '故乡',
  },
  {
    field: 'mark',
    label: '个性签名',
  },
  {
    field: 'email',
    label: '邮箱',
  },
  {
    field: 'job',
    label: '工作行业',
  },
  {
    field: 'job',
    label: '职位',
  },
  {
    field: 'extraInfo',
    label: '附加信息',
  },
  {
    field: 'point',
    label: '积分',
  },
  {
    field: 'verifyStatus',
    label: '认证状态',
    render: (curVal) => {
      return useRender.renderDict(curVal, DICT_TYPE.MEMBER_AUTH_STATUS)
    },
  },
  {
    field: 'status',
    label: '封停状态',
    render: (curVal) => {
      return useRender.renderDict(curVal, DICT_TYPE.MEMBER_STATUS)
    },
  },
  {
    field: 'serviceArea',
    label: '来源服务区',
  },
  {
    field: 'createTime',
    label: '创建时间',
    render: (curVal) => {
      return useRender.renderDate(curVal)
    },
  },
  {
    field: 'carType',
    label: '车型',
  },
  {
    field: 'carNumber',
    label: '车牌',
  },
  {
    field: 'registerSource',
    label: '注册来源',
  },
  {
    field: 'groupStatus',
    label: '入群状态',
    render: (curVal) => {
      return useRender.renderDict(curVal, DICT_TYPE.MEMBER_JOIN_STATUS)
    },
  },
  {
    field: 'clubRoleCodes',
    label: '管理员角色',
  },
  {
    field: 'status',
    label: '账号状态',
    render: (curVal) => {
      return useRender.renderDict(curVal, DICT_TYPE.MEMBER_STATUS)
    },
  },

]

export const carSchema: DescItem[] = [
  {
    field: 'carOwner',
    label: '车主姓名',
  },
  {
    field: 'isOwner',
    label: '是否本人名下',
    render: (curVal) => {
      return useRender.renderDict(curVal, DICT_TYPE.CAR_IS_OWNER)
    },
  },
  {
    field: 'carBrand',
    label: '车品牌',
  },
  {
    field: 'carTrain',
    label: '车系',
  },
  {
    field: 'carType',
    label: '车型',
  },
  {
    field: 'carColor',
    label: '颜色',
  },
  {
    field: 'carNumber',
    label: '车牌',
  },
  {
    field: 'carphoto',
    label: '整车照片',
    render: (curVal) => {
      return useRender.renderImg(curVal)
    },
  },
  {
    field: 'drivingLicence',
    label: '行驶证',
    render: (curVal) => {
      return useRender.renderImg(curVal)
    },
  },
  {
    field: 'drivingcard',
    label: '驾驶证',
    render: (curVal) => {
      return useRender.renderImg(curVal)
    },
  },
  {
    field: 'registerDate',
    label: '行驶证注册日期',
    render: (curVal) => {
      return useRender.renderDate(curVal)
    },
  },
  {
    field: 'issueDate',
    label: '行驶证发证日期',
    render: (curVal) => {
      return useRender.renderDate(curVal)
    },
  },
  {
    field: 'createTime',
    label: '创建时间',
    render: (curVal) => {
      return useRender.renderDate(curVal)
    },
  },
]

export const integralSchema: BasicColumn[] = [
  {
    title: '微信昵称',
    dataIndex: 'nickname',
  },
  {
    title: 'ClubID',
    dataIndex: 'clubId',
  },
  {
    title: '手机号码',
    dataIndex: 'mobile',
  },
  {
    title: '积分类型',
    dataIndex: 'bizType',
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.MEMBER_POINT_BIZ_TYPE)
    },
  },
  {
    title: '收入',
    dataIndex: 'income',
  },
  {
    title: '支出',
    dataIndex: 'expense',
  },
  {
    title: '结余',
    dataIndex: 'totalPoint',
  },
  {
    title: '积分流水号',
    dataIndex: 'pointNo',
    width: 200,
  },
  {
    title: '积分摘要',
    dataIndex: 'title',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
  },
]

export const createFormSchema: FormSchema[] = [
  {
    label: '会员ID',
    field: 'userId',
    component: 'Input',
    show: false,
  },
  {
    label: '积分类型',
    field: 'bizType',
    component: 'Select',
    required: true,
    componentProps: {
      options: getDictOptions(DICT_TYPE.MEMBER_POINT_BIZ_TYPE),
      placeholder: '请选择积分类型',
    },
  },
  {
    label: '会员',
    field: 'nickname',
    component: 'Input',
    required: true,
    dynamicDisabled: true,
  },
  {
    label: '积分值',
    field: 'point',
    helpMessage: '积分值，扣除为负数',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '摘要信息',
    field: 'title',
    component: 'InputTextArea',
    required: true,
  },
  {
    label: '备注',
    field: 'description',
    component: 'InputTextArea',
  },
]

export const userGroupSchema: FormSchema[] = [
  {
    label: '头像',
    field: 'avatar',
    component: 'FileUpload',
    dynamicDisabled: true,
    componentProps: {
      maxCount: 1,
      fileType: 'image',
    },
  },
  {
    label: '微信昵称',
    field: 'nickname',
    component: 'Input',
    required: true,
    dynamicDisabled: true,
  },
  {
    label: '角色',
    field: 'role',
    component: 'Select',
    required: true,
    componentProps: {
      mode: 'multiple',
      options: [
        {
          label: '管理员',
          value: 'admin',
        },
        {
          label: '会员',
          value: 'member',
        },
      ],
    },
  },
]
