<script lang="ts" setup>
import { ref } from 'vue'
import { integralSchema } from './user.data'
import AddIntegral from './AddIntegral.vue'
import { useI18n } from '@/hooks/web/useI18n'
import { BasicModal, useModal, useModalInner } from '@/components/Modal'
import { BasicTable, useTable } from '@/components/Table'
import { getScorePage } from '@/api/club'
import { IconEnum } from '@/enums/appEnum'

defineOptions({ name: 'OperLogInfoModal' })

const { t } = useI18n()
const memberId = ref()
const nickname = ref()
const [registerModalInner, { closeModal }] = useModalInner((record: Recordable) => {
  memberId.value = record.id
  nickname.value = record.nickname
})

const [registerTable, { reload }] = useTable({
  title: '',
  api: () => getScorePage({ userId: memberId.value }),
  columns: integralSchema,
  useSearchForm: false,
  showTableSetting: true,
})

const [registerModal, { openModal }] = useModal()
function handleCreate() {
  openModal(true, { isUpdate: false, memberId: memberId.value, nickname: nickname.value })
}
</script>

<template>
  <BasicModal v-bind="$attrs" title="积分明细" width="1200px" @register="registerModalInner" @ok="closeModal">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" :pre-icon="IconEnum.ADD" @click="handleCreate">
          {{ t('action.create') }}
        </a-button>
        <a-button type="primary" :pre-icon="IconEnum.REFRESH" @click="reload">
          {{ t('action.refresh') }}
        </a-button>
      </template>
    </BasicTable>
    <AddIntegral @register="registerModal" @success="reload()" />
  </BasicModal>
</template>
