<script lang="ts" setup>
import { formSchema } from './user.data'
import { useI18n } from '@/hooks/web/useI18n'
import { BasicForm, useForm } from '@/components/Form'
import { BasicModal, useModalInner } from '@/components/Modal'
import { authAudit, getUser } from '@/api/member/user'

defineOptions({ name: 'MemberUserModal' })

const emit = defineEmits(['success', 'register'])
const { t } = useI18n()

const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: formSchema,
  showActionButtonGroup: false,
  actionColOptions: { span: 23 },
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields()
  setModalProps({ confirmLoading: false })
  const res = await getUser(data.record.id)
  setFieldsValue({ ...res })
})

// 审核状态
async function handleAudit(status: number) {
  const values = await validate()
  console.log(values)
  authAudit({
    id: values.id,
    status,
  })
  closeModal()
  emit('success')
  setModalProps({ confirmLoading: false })
}

async function handleSubmit() {
  closeModal()
  emit('success')
  setModalProps({ confirmLoading: false })
}

function handleClose() {
  closeModal()
}
</script>

<template>
  <BasicModal v-bind="$attrs" :title="t('action.authInfo')" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
    <!-- 确定按钮自定义 -->
    <template #footer>
      <a-button type="primary" @click="handleAudit(2)">
        {{ t('action.pass') }}
      </a-button>
      <a-button @click="handleAudit(3)">
        {{ t('action.refuse') }}
      </a-button>
      <a-button @click="handleClose">
        {{ t('action.cancel') }}
      </a-button>
    </template>
  </BasicModal>
</template>
