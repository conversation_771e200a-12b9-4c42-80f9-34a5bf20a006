<script lang="ts" setup>
import { ref } from 'vue'
import UserModal from './UserModal.vue'
import UserDetail from './UserDetail.vue'
import CarDetail from './CarDetail.vue'
import IntegralDetail from './IntegralDetail.vue'
import UserGroupModal from './UserGroupModal.vue'
import { columns, searchFormSchema } from './user.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { getUserList, updateUserInfo } from '@/api/member/user'

defineOptions({ name: 'MemberUser' })

const { t } = useI18n()

// 用户详情
const [registerUserDetailModal, { openModal: openUserDetailModal }] = useModal()
function handleShowUserDetail(record: Recordable) {
  openUserDetailModal(true, record)
}

// 认证信息
const [registerAuthInfoModal, { openModal: openAuthInfoModal }] = useModal()
function handleShowAuthInfo(record: Recordable) {
  openAuthInfoModal(true, { record, isUpdate: true })
}

// 设置用户组
const [registerUserGroupModal, { openModal: openUserGroupModal }] = useModal()
function handleShowUserGroup(record: Recordable) {
  openUserGroupModal(true, record)
}

// 车辆详情
const [registerCarDetailModal, { openModal: openCarDetailModal }] = useModal()
function handleShowCarDetail(record: Recordable) {
  openCarDetailModal(true, record)
}

// 更新用户状态
function handleUpdateStatus(record: Recordable, status: number) {
  updateUserInfo({ ...record, status: status === 0 ? 1 : 0 })
}

// 积分明细
const [registerIntegralDetailModal, { openModal: openIntegralDetailModal }] = useModal()
function handleShowIntegralDetail(record: Recordable) {
  openIntegralDetailModal(true, record)
}

const [registerTable] = useTable({
  title: '会员管理',
  api: getUserList,
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  actionColumn: {
    width: 400,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  },
  rowKey: 'id',
  rowSelection: { type: 'checkbox' },
})
</script>

<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            { icon: IconEnum.VIEW, label: t('action.detail'), onClick: handleShowUserDetail.bind(null, record) },
            {
              icon: IconEnum.EDIT,
              label: t('action.setUserGroup'),
              onClick: handleShowUserGroup.bind(null, record),
            },
            {
              icon: IconEnum.EDIT,
              label: t('action.authInfo'),
              ifShow: record.status !== 3,
              onClick: handleShowAuthInfo.bind(null, record),
            },
            { label: t('action.carDetail'), onClick: handleShowCarDetail.bind(null, record) },
            {
              danger: true,
              label: t('action.disable'),
              ifShow: record.status === 0 && record.status !== 3,
              popConfirm: {
                title: t('common.confirmDisableMessage'),
                placement: 'left',
                confirm: () => handleUpdateStatus(record, 1),
              },
            },
            {
              label: t('action.enable'),
              ifShow: record.status === 1 && record.status !== 3,
              popConfirm: {
                title: t('common.confirmEnableMessage'),
                placement: 'left',
                confirm: () => handleUpdateStatus(record, 0),
              },
            },
            {
              icon: IconEnum.EDIT,
              label: t('action.integralDetail'),
              onClick: handleShowIntegralDetail.bind(null, record),
            },

          ]" />
        </template>
      </template>
    </BasicTable>
    <UserModal @register="registerAuthInfoModal" @success="reload()" />
    <UserDetail @register="registerUserDetailModal" />
    <CarDetail @register="registerCarDetailModal" />
    <IntegralDetail @register="registerIntegralDetailModal" />
    <UserGroupModal @register="registerUserGroupModal" />
  </div>
</template>

<style lang="less" scoped>
/* 在你的全局样式文件中添加 */
:deep(.ant-table-row-selected td) {
  background-color: rgb(250 250 250) !important;
}
</style>
