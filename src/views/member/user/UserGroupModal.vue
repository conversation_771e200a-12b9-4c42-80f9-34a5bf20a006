<script setup lang="ts">
import { userGroupSchema } from './user.data'
import { BasicModal, useModalInner } from '@/components/Modal'
import { BasicForm, useForm } from '@/components/Form'
import { useI18n } from '@/hooks/web/useI18n'
import type { UserVO } from '@/api/member/user'
import { updateUser } from '@/api/member/user'
import { useMessage } from '@/hooks/web/useMessage'

defineOptions({ name: 'UserGroupModal' })

const emit = defineEmits(['success'])
const { t } = useI18n()
const message = useMessage()

const [registerForm, { validate, setFieldsValue }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: userGroupSchema,
  showActionButtonGroup: false,
  actionColOptions: { span: 23 },
})

// 记录数据
const record = ref<UserVO>()
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  record.value = data
  setFieldsValue({
    ...data,
  })
  setModalProps({ confirmLoading: false })
})

async function handleSubmit() {
  const values = await validate() as UserVO
  try {
    setModalProps({ confirmLoading: true })
    updateUser({ ...record.value, ...values })
    closeModal()
    message.createMessage.success(t('common.success'))
    emit('success')
  }
  catch (error) {
    console.log(error)
  }
  finally {
    setModalProps({ confirmLoading: false })
  }
}
</script>

<template>
  <BasicModal v-bind="$attrs" :title="t('action.setUserGroup')" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
