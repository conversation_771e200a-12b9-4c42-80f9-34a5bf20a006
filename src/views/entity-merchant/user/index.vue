<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { columns, searchFormSchema } from './data'
import { useI18n } from '@/hooks/web/useI18n'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { IconEnum } from '@/enums/appEnum'
import { getEntityPage } from '@/api/entity/user/index'

defineOptions({ name: 'EntityUser' })
const router = useRouter()
const { t } = useI18n()

const [registerTable, { reload }] = useTable({
  title: '商家-经营主体信息',
  api: getEntityPage,
  columns,
  formConfig: { labelWidth: 135, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 140,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  },
})

// 新增
function handleCreate() {
  router.push({
    name: 'EntityMerchantUserModel',
  })
}

// 编辑
function handleEdit(record: any) {
  router.push({
    name: 'EntityMerchantUserModel',
    query: { id: record.id },
  })
}

// 详情
function handleDetail(record: any) {
  router.push({
    name: 'DetailModel',
    query: { id: record.id },
  })
}
</script>

<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button v-auth="['platform:biz-entities:create']" type="primary" :pre-icon="IconEnum.ADD"
          @click="handleCreate">
          {{ t('action.create') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              label: t('action.edit'),
              danger: true,
              ifShow: record.verified === 3 && record.submitStatus === 0,
              auth: 'platform:biz-entities:update',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: IconEnum.EDIT,
              label: t('action.redact'),
              ifShow: record.verified === 0 && record.submitStatus === 0,
              auth: 'platform:biz-entities:update',
              onClick: handleEdit.bind(null, record),
            },
            { icon: IconEnum.VIEW, label: t('action.detail'), auth: 'platform:biz-entities:query', onClick: handleDetail.bind(null, record) },
          ]" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
