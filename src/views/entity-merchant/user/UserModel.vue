<script setup lang="ts">
import { Modal, message } from 'ant-design-vue'
import { ExclamationCircleTwoTone, PlusOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import type { FormInstance, UploadFile } from 'ant-design-vue'
import type { Dayjs } from 'dayjs'
import { LicenseTypeOptions } from './data'
import BaseInfo from '@/views/entity/user/components/BaseInfo/index.vue'
import { useGlobSetting } from '@/hooks/setting'
import { getAccessToken, getTenantId } from '@/utils/auth'
import { commonUpload } from '@/utils/file/upload'
import { handleTree as handleTreeFn } from '@/utils/tree'
import { addEntity, updateEntity } from '@/api/entity/user'
import { getRegion } from '@/api/system/area'
import { getEntity } from '@/api/entity/user/index'
import { useRender } from '@/components/Table'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { banCardOcrApi, getEplBankList } from '@/api/merchant/auth'

defineOptions({
  name: 'EntityMerchantUserModel',
})

const Promise = globalThis.Promise

const router = useRouter()
const route = useRoute()

const id = route.query.id

// 上传地址
const uploadUrl = useGlobSetting().uploadUrl
const headers = reactive({
  'Authorization': `Bearer ${getAccessToken()}`,
  'tenant-id': getTenantId(),
})

const dateFormat = 'YYYY-MM-DD'

// 表单引用
const formRef = ref<FormInstance>()

// 提交状态
const submitting = ref(false)

// 受益人类型定义
interface Beneficiary {
  _key: number // 唯一key
  beneficiaryName: string
  beneficiaryIdNumber: string
  percentage: number | undefined
  beneficiaryIdCardFront: UploadFile[]
  beneficiaryIdCardBack: UploadFile[]
  certificateStartDate: Dayjs | null
  certificateEndDate: Dayjs | null
  idLongTerm: boolean
  address: string[]
  beneficiaryContactAddress: string
}

// 公户信息
interface BankAccount {
  accountName: string
  accountNo: string
  bankName: string
  bankBranchName: string
  openBankCityStr: string[]
  attachments: UploadFile[]
  openBankCode: string
}

// 附件
interface Attachments {
  businessLicensePhoto: UploadFile[]
  rentalAgreementFile: UploadFile[]
  legalIdPhotoFront: UploadFile[]
  legalIdPhotoEnd: UploadFile[]
  bizAuthorizationLetterFile: UploadFile[]
  contactPhotoFront: UploadFile[]
  contactPhotoEnd: UploadFile[]
  materialMiniProgram: UploadFile[]
}

// 表单数据
const formData = reactive({
  // 审核相关字段
  verified: 0, // 审核状态
  submitStatus: 0, // 提交状态
  submitComment: '', // 驳回原因
  // 经营主体信息
  name: '',
  shortName: '',
  licenseType: 1,
  licenseNo: '',
  licenseValidFrom: null as Dayjs | null,
  licenseValidTo: null as Dayjs | null,
  licenseLongTerm: false,
  registerCapital: undefined as number | undefined,
  registerAddress: '',
  bizScope: '',
  operatingAddress: [] as string[],
  bizAddress: '',

  // 法人信息
  legalName: '',
  legalIdno: '',
  legalIdValidFrom: null as Dayjs | null,
  legalIdValidTo: null as Dayjs | null,
  legalIdLongTerm: false,
  legalPhone: '',
  legalAddress: [] as string[],
  legalIdAddress: '',

  // 联系人信息
  contactName: '',
  contactPhone: '',
  contactIdno: '',
  contactSameAsLegal: true,
  contactEmail: '',
  contactIdValidFrom: null as Dayjs | null,
  contactIdValidTo: null as Dayjs | null,
  contactIdLongTerm: false,

  // 受益人信息
  beneficiaries: [] as Beneficiary[],

  // 公户信息
  bankAccount: {
    attachments: [] as UploadFile[],
    accountName: '',
    accountNo: '',
    bankName: '',
    openBankCityStr: [],
    bankBranchName: '',
    openBankCode: '',
  } as BankAccount,

  // 所有附件
  attachments: {
    businessLicensePhoto: [] as UploadFile[],
    rentalAgreementFile: [] as UploadFile[],
    legalIdPhotoFront: [] as UploadFile[],
    legalIdPhotoEnd: [] as UploadFile[],
    bizAuthorizationLetterFile: [] as UploadFile[],
    contactPhotoFront: [] as UploadFile[],
    contactPhotoEnd: [] as UploadFile[],
    materialMiniProgram: [] as UploadFile[],
  } as Attachments,
})
// 提交确认表单数据
const viewFormData = ref({})
// 点击提交按钮状态
const submitStatus = ref(true)

// 开户银行选项
const subBankOptions = ref([])
async function fetchSubBank() {
  const { bankName, openBankCityStr } = formData.bankAccount
  if (!bankName || !openBankCityStr[0] || !openBankCityStr[1])
    return
  const res = await getEplBankList({
    bank: bankName,
    province: formData.bankAccount.openBankCityStr[0],
    city: formData.bankAccount.openBankCityStr[1],
  })
  subBankOptions.value = res.map(item => ({
    ...item,
    label: item.name,
    value: item.name,
  })) || []
}

const BankNameOptions = computed(() => {
  return getDictOptions(DICT_TYPE.EPL_BANK_LIST, 'string').map((item) => {
    return {
      ...item,
      value: item.label,
    }
  })
})

function handleRenderDisplay({ labels, selectedOptions }) {
  if (selectedOptions.length)
    return labels.join(' / ')
  return ''
}

const changeOpenBankCity = () => {
  formData.bankAccount.bankBranchName = ''
  fetchSubBank()
}

// 地址选项
interface Option {
  value: string
  label: string
  loading?: boolean
  isLeaf?: boolean
  children?: Option[]
}
const addressOptions = ref<Option[]>([])
async function fetchArea() {
  const res = await getRegion()
  addressOptions.value = handleTreeFn(res, 'code', 'parentCode', 'children')
  console.log(`output->`, addressOptions.value)
}
fetchArea()

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入经营主体名称', trigger: 'blur' }],
  shortName: [{ required: true, message: '请输入经营简称', trigger: 'blur' }],
  licenseType: [{ required: true, message: '请选择主体类型', trigger: 'change' }],
  operatingAddress: [{ type: 'array', required: true, message: '请选择经营地址', trigger: 'change' }],
  bizAddress: [{ required: true, message: '请输入详细经营地址', trigger: 'blur' }],
  contactSameAsLegal: [{ required: true, message: '请选择联系人是否与法人一致', trigger: 'change' }],
  legalAddress: [{ required: true, message: '请选择省市区', trigger: 'change' }],
  legalIdAddress: [{ required: true, message: '请输入法人联系详细地址', trigger: 'blur' }],
  licenseNo: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
  registerCapital: [{ required: true, message: '请输入注册资本', trigger: 'blur' }],
  registerAddress: [{ required: true, message: '请输入注册地址', trigger: 'blur' }],
  bizScope: [{ required: true, message: '请输入经营范围', trigger: 'blur' }],
  attachments: {
    businessLicensePhoto: [{
      required: true,
      validator: (_rule: any, value: UploadFile[]) => {
        if (!value || value.length === 0)
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject('请上传营业执照')

        return Promise.resolve()
      },
      trigger: 'change',
    }],
    legalIdPhotoFront: [{
      required: true,
      validator: (_rule: any, value: UploadFile[]) => {
        if (!value || value.length === 0)
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject('请上传法人身份证正面')

        return Promise.resolve()
      },
      trigger: 'change',
    }],
    legalIdPhotoEnd: [{
      required: true,
      validator: (_rule: any, value: UploadFile[]) => {
        if (!value || value.length === 0)
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject('请上传法人身份证反面')

        return Promise.resolve()
      },
      trigger: 'change',
    }],
    contactPhotoFront: [{ required: true, message: '请上传联系人身份证正面', trigger: 'change' }],
    contactPhotoEnd: [{ required: true, message: '请上传联系人身份证反面', trigger: 'change' }],
    bizAuthorizationLetterFile: [{ type: 'array', required: true, message: '请上传业务办理授权函', trigger: 'change' }],
    materialMiniProgram: [{ type: 'array', required: true, message: '请上传小程序截图', trigger: 'change' }],
  },
  legalName: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
  legalIdno: [{ required: true, message: '请输入身份证号码', trigger: 'blur' }],
  legalPhone: [{ required: true, message: '请输入手机号码', trigger: 'blur' }],
  contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '请输入手机号码', trigger: 'blur' }],
  contactIdno: [{ required: true, message: '请输入身份证号码', trigger: 'blur' }],
  contactEmail: [{ required: true, message: '请输入联系人邮箱', trigger: 'blur' }],
  bankAccount: {
    attachments: [{
      required: true,
      type: 'array',
      validator: async (_rule: any) => {
        if (!formData.bankAccount?.attachments || formData.bankAccount?.attachments.length === 0)
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject('请上传证明文件')
        return Promise.resolve()
      },
      trigger: 'change',
    }],
    accountName: [{ required: true, message: '请输入账户名', trigger: 'blur' }],
    accountNo: [{ required: true, message: '请输入账户号', trigger: 'blur' }],
    bankName: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
    openBankCityStr: [{ required: true, message: '请选择开户城市', trigger: 'change' }],
    bankBranchName: [{ required: true, message: '请输入开户支行', trigger: 'blur' }],
    openBankCode: [{ required: true, message: '请输入支行银联号', trigger: 'blur' }],
  },
  // 证照有效期校验
  licenseValidFrom: [{ required: true, message: '请选择证件开始日期', trigger: 'change' }],
  licenseValidTo: [
    { required: true, message: '请选择证件结束日期', trigger: 'change' },
    {
      validator: (_rule: any, value: any) => {
        if (!formData.licenseLongTerm && value && formData.licenseValidFrom && value < formData.licenseValidFrom)
          return Promise.reject(new Error('结束日期不能早于开始日期'))
        return Promise.resolve()
      },
      trigger: 'change',
    },
  ],
  // 法人证件有效期校验
  legalIdValidFrom: [{ required: true, message: '请选择证件开始日期', trigger: 'change' }],
  legalIdValidTo: [
    { required: true, message: '请选择证件结束日期', trigger: 'change' },
    {
      validator: (_rule: any, value: any) => {
        if (!formData.legalIdLongTerm && value && formData.legalIdValidFrom && value < formData.legalIdValidFrom)
          return Promise.reject(new Error('结束日期不能早于开始日期'))
        return Promise.resolve()
      },
      trigger: 'change',
    },
  ],
  // 联系人证件有效期校验规则
  contactIdValidFrom: [{ required: true, message: '请选择证件开始日期', trigger: 'change' }],
  contactIdValidTo: [
    { required: true, message: '请选择证件结束日期', trigger: 'change' },
    {
      validator: (_rule: any, value: any) => {
        if (!formData.contactIdLongTerm && value && formData.contactIdValidFrom && value < formData.contactIdValidFrom)
          return Promise.reject(new Error('结束日期不能早于开始日期'))
        return Promise.resolve()
      },
      trigger: 'change',
    },
  ],
}

// 处理证照长期有效
const handleLicenseLongTermChange = (checked: boolean) => {
  if (checked)
    formData.licenseValidTo = null
  // 清除下校验结果
  formRef.value?.clearValidate(['licenseValidTo'])
}

// 处理法人证件长期有效
const handleLegalIdLongTermChange = (checked: boolean) => {
  if (checked)
    formData.legalIdValidTo = null
  // 清除下校验结果
  formRef.value?.clearValidate(['legalIdValidTo'])
}

// 处理受益人证件长期有效
const handleBeneficiaryLongTermChange = (beneficiary: Beneficiary, checked: boolean, index: number) => {
  if (checked)
    beneficiary.certificateEndDate = null
  setTimeout(() => {
    formRef.value?.clearValidate(`beneficiaries.${index}.certificateEndDate`)
    beneficiary._key = Date.now() + Math.random() // 强制刷新
  }, 0)
}

// 处理联系人与法人一致变化
const handleContactSameAsLegalChange = (value: boolean) => {
  if (value) {
    // 自动填充联系人信息
    formData.contactName = formData.legalName
    formData.contactPhone = formData.legalPhone
    formData.contactIdno = formData.legalIdno
    formData.attachments.bizAuthorizationLetterFile = []
  }
}

// 处理联系人证件长期有效
const handleContactIdLongTermChange = (checked: boolean) => {
  if (checked)
    formData.contactIdValidTo = null
  // 清除下校验结果
  formRef.value?.clearValidate(['contactIdValidTo'])
}

// 通用上传处理函数（customRequest 方案）
const handleCommonCustomRequest = (targetArr: UploadFile[], fieldName: string, optionsConfig: { maxSizeMB?: number, acceptTypes?: string[] } = {}) => {
  return async (uploadOptions: any) => {
    const { file, onSuccess, onError } = uploadOptions
    const maxSize = optionsConfig.maxSizeMB || 10
    const acceptTypes = optionsConfig.acceptTypes || ['jpg', 'jpeg', 'png', 'pdf']
    const ext = file.name.split('.').pop()?.toLowerCase()
    const isAccept = ext && acceptTypes.includes(ext)
    if (!isAccept) {
      message.error(`文件格式不支持，只允许: ${acceptTypes.join(', ')}`)
      onError && onError(new Error('文件格式不支持'))
      return
    }
    const isLt = file.size / 1024 / 1024 < maxSize
    if (!isLt) {
      message.error(`${fieldName}大小不能超过${maxSize}MB!`)
      onError && onError(new Error('文件过大'))
      return
    }
    try {
      const params = { file, biz: 'temp' }
      const result = await commonUpload(params, undefined, uploadUrl)
      if (result && result.data) {
        onSuccess && onSuccess({ url: result.data }, file)
        message.success(`${fieldName}上传成功`)
      }
      else {
        message.error('上传失败')
        onError && onError(new Error('上传失败'))
      }
    }
    catch (e) {
      message.error('上传失败')
      onError && onError(e)
    }
  }
}

// 添加受益人
const addBeneficiary = () => {
  formData.beneficiaries.push({
    _key: Date.now() + Math.random(), // 唯一key
    beneficiaryName: '',
    beneficiaryIdNumber: '',
    percentage: undefined as number | undefined,
    beneficiaryIdCardFront: [] as UploadFile[],
    beneficiaryIdCardBack: [] as UploadFile[],
    certificateStartDate: null,
    certificateEndDate: null,
    idLongTerm: false,
    address: [] as string[],
    beneficiaryContactAddress: '',
  })
}

// 删除受益人
const removeBeneficiary = (index: number) => {
  formData.beneficiaries.splice(index, 1)
}

// 下载模板
const downloadTemplate = () => {
  console.log('下载模板')
}

// 重置表单
const resetForm = () => {
  Modal.confirm({
    title: '确认重置',
    content: '确定要重置所有表单数据吗？',
    onOk: () => {
      formRef.value?.resetFields()
      // 受益人需要特殊处理
      formData.beneficiaries = []

      // 联系人需要特殊处理
      formData.attachments.contactPhotoFront = []
      formData.attachments.contactPhotoEnd = []
      formData.attachments.bizAuthorizationLetterFile = []
      formData.contactName = ''
      formData.contactPhone = ''
      formData.contactIdno = ''
      formData.contactIdValidFrom = null
      formData.contactIdValidTo = null
      formData.contactIdLongTerm = false
      message.success('表单已重置')
    },
  })
}

// 保存草稿
const saveDraft = async () => {
  try {
    // 这里调用保存草稿API
    console.log('保存草稿数据:', formData)
    message.success('草稿保存成功')
  }
  catch (error) {
    message.error('保存草稿失败')
  }
}

// 再次确认，展示详情页面
const submitForm = async () => {
  try {
    submitting.value = true
    await formRef.value?.validate()

    const params = {
      // 经营主体信息
      name: formData.name,
      shortName: formData.shortName,
      licenseType: formData.licenseType,
      licenseNo: formData.licenseNo,
      licenseValidFrom: formData.licenseValidFrom,
      licenseValidTo: formData.licenseLongTerm ? null : formData.licenseValidTo,
      registerCapital: formData.registerCapital,
      registerAddress: formData.registerAddress,
      bizScope: formData.bizScope,
      province: formData.operatingAddress?.[0] || '',
      city: formData.operatingAddress?.[1] || '',
      district: formData.operatingAddress?.[2] || '',
      bizAddress: formData.bizAddress,

      // 法人信息
      legalName: formData.legalName,
      legalIdno: formData.legalIdno,
      legalIdValidFrom: formData.legalIdValidFrom,
      legalIdValidTo: formData.legalIdLongTerm ? null : formData.legalIdValidTo,
      legalPhone: formData.legalPhone,
      legalIdAddress: formData.legalIdAddress,
      legalIdProvince: formData.legalAddress?.[0] || '',
      legalIdCity: formData.legalAddress?.[1] || '',
      legalIdDistrict: formData.legalAddress?.[2] || '',

      // 联系人信息
      contactName: formData.contactName,
      contactIdno: formData.contactIdno,
      contactIdValidFrom: formData.contactIdValidFrom,
      contactIdValidTo: formData.contactIdLongTerm ? null : formData.contactIdValidTo,
      contactIdLongTerm: formData.contactIdLongTerm,
      contactPhone: formData.contactPhone,
      contactEmail: formData.contactEmail,
      contactSameAsLegal: formData.contactSameAsLegal,

      // 受益人信息
      beneficiaries: formData.beneficiaries.map(item => ({
        beneficiaryName: item.beneficiaryName,
        beneficiaryIdNumber: item.beneficiaryIdNumber,
        percentage: item.percentage,
        beneficiaryIdCardFront: item.beneficiaryIdCardFront.map(f => f?.response?.url)?.join(',') || '',
        beneficiaryIdCardBack: item.beneficiaryIdCardBack.map(f => f?.response?.url)?.join(',') || '',
        certificateStartDate: item.certificateStartDate,
        certificateEndDate: item.idLongTerm ? null : item.certificateEndDate,
        idLongTerm: item.idLongTerm,
        beneficiaryContactProvince: item.address?.[0] || '',
        beneficiaryContactCity: item.address?.[1] || '',
        beneficiaryContactDistrict: item.address?.[2] || '',
        beneficiaryContactAddress: item.beneficiaryContactAddress,
      })),

      // 公户信息
      bankAccount: {
        id: 4,
        attachments: formData.bankAccount.attachments.map(f => f?.response?.url)?.join(',') || '',
        accountName: formData.bankAccount.accountName,
        accountNo: formData.bankAccount.accountNo,
        bankName: formData.bankAccount.bankName,
        openBankProvince: formData.bankAccount.openBankCityStr[0],
        openBankCity: formData.bankAccount.openBankCityStr[1],
        bankBranchName: formData.bankAccount.bankBranchName,
      },
      // 附件
      attachments: {
        businessLicensePhoto: formData.attachments.businessLicensePhoto.map(f => f.response?.url)?.join(','),
        rentalAgreementFile: formData.attachments.rentalAgreementFile.map(f => f.response?.url)?.join(','),
        legalIdPhotoFront: formData.attachments.legalIdPhotoFront.map(f => f.response?.url)?.join(','),
        legalIdPhotoEnd: formData.attachments.legalIdPhotoEnd.map(f => f.response?.url)?.join(','),
        contactPhotoFront: formData.attachments.contactPhotoFront.map(f => f.response?.url)?.join(','),
        contactPhotoEnd: formData.attachments.contactPhotoEnd.map(f => f.response?.url)?.join(','),
        bizAuthorizationLetterFile: formData.attachments.bizAuthorizationLetterFile.map(f => f.response?.url)?.join(','),
        materialMiniProgram: formData.attachments.materialMiniProgram.map(f => f.response?.url),
      },
    }
    viewFormData.value = params
    submitStatus.value = false
  }
  catch (error) {
    setTimeout(() => {
      const firstError = formRef.value?.$el?.querySelector('.ant-form-item-explain-error')
      if (firstError)
        firstError.scrollIntoView() // 立即滚动
    }, 0)
    console.error('表单验证失败:', error)
  }
  finally {
    submitting.value = false
  }
}

// 确认后，调用接口，新函数
const submitConfirm = async () => {
  try {
    if (id)
      await updateEntity({ ...viewFormData.value, id })

    else
      await addEntity(viewFormData.value)
    message.success('表单提交成功')
  }
  catch (error) {
    message.error('提交失败，请重试')
  }
  finally {
    router.push({
      path: '/entity/user',
    })
  }
}

const initFormData = async () => {
  // 模拟数据
  const id = route.query.id as string
  if (id) {
    const res = await getEntity(id)
    console.log('获取经营主体信息', res)
    const mockData = {
      ...res,

      // 审核相关字段
      licenseLongTerm: !res.licenseValidTo,
      operatingAddress: [res.province, res.city, res.district].filter(item => item),

      // 法人信息
      legalIdLongTerm: !res.legalIdValidTo,
      legalAddress: [res.legalIdProvince, res.legalIdCity, res.legalIdDistrict].filter(item => item),

      // 联系人信息
      contactIdLongTerm: !res.contactIdValidTo,
      contactSameAsLegal: res.contactIdno === res.legalIdno,

      // 受益人信息
      beneficiaries: res.beneficiaries.map(item => ({
        ...item,
        beneficiaryIdCardFront: item.beneficiaryIdCardFront
          ? [{
            uid: '1',
            name: '受益人身份证正面.jpg',
            url: typeof item.beneficiaryIdCardFront === 'string' ? item.beneficiaryIdCardFront : '',
          }]
          : [],
        beneficiaryIdCardBack: item.beneficiaryIdCardBack
          ? [{
            uid: '1',
            name: '受益人身份证反面.jpg',
            url: typeof item.beneficiaryIdCardBack === 'string' ? item.beneficiaryIdCardBack : '',
          }]
          : [],
        address: [item.beneficiaryContactProvince, item.beneficiaryContactCity, item.beneficiaryContactDistrict].filter(item => item),
        idLongTerm: !item.certificateEndDate,
      })),

      // 公户信息
      bankAccount: {
        id: 4,
        ...res.bankAccount,
        attachments: res.attachments.bankAccountAttachments
          ? [{
            uid: '1',
            name: '证明文件.jpg',
            url: typeof res.attachments.bankAccountAttachments === 'string' ? res.attachments.bankAccountAttachments : '',
          }]
          : [],
        accountName: res.bankAccount?.accountName || '',
        accountNo: res.bankAccount?.accountNo || '',
        bankName: res.bankAccount?.bankName || undefined,
        openBankCityStr: [res.bankAccount?.openBankProvince, res.bankAccount?.openBankCity].filter(item => item),
        bankBranchName: res.bankAccount?.bankBranchName,
        openBankCode: res.bankAccount?.openBankCode,
      },

      // 附件
      attachments: {
        businessLicensePhoto: res.attachments.businessLicensePhoto
          ? [
            {
              uid: '1',
              name: '营业执照.jpg',
              url: typeof res.attachments.businessLicensePhoto === 'string' ? res.attachments.businessLicensePhoto : '',
            },
          ]
          : [],
        rentalAgreementFile: res.attachments.rentalAgreementFile
          ? [{
            uid: '1',
            name: '租赁协议.zip',
            url: typeof res.attachments.rentalAgreementFile === 'string' ? res.attachments.rentalAgreementFile : '',
          }]
          : [],
        legalIdPhotoFront: res.attachments.legalIdPhotoFront
          ? [{
            uid: '1',
            name: '法人身份证正面.jpg',
            url: typeof res.attachments.legalIdPhotoFront === 'string' ? res.attachments.legalIdPhotoFront : '',
          }]
          : [],
        contactPhotoFront: res.attachments.contactPhotoFront
          ? [{
            uid: '1',
            name: '联系人身份证正面.jpg',
            url: typeof res.attachments.contactPhotoFront === 'string' ? res.attachments.contactPhotoFront : '',
          }]
          : [],
        legalIdPhotoEnd: res.attachments.legalIdPhotoEnd
          ? [{
            uid: '1',
            name: '法人身份证反面.jpg',
            url: typeof res.attachments.legalIdPhotoEnd === 'string' ? res.attachments.legalIdPhotoEnd : '',
          }]
          : [],
        bizAuthorizationLetterFile: res.attachments.bizAuthorizationLetterFile
          ? [{
            uid: '1',
            name: `业务办理授权函.${res.attachments?.bizAuthorizationLetterFile?.split('.')?.pop() || ''}`,
            url: typeof res.attachments.bizAuthorizationLetterFile === 'string' ? res.attachments.bizAuthorizationLetterFile : '',
          }]
          : [],
        contactPhotoEnd: res.attachments.contactPhotoEnd
          ? [{
            uid: '1',
            name: '联系人身份证正面.jpg',
            url: typeof res.attachments.contactPhotoEnd === 'string' ? res.attachments.contactPhotoEnd : '',
          }]
          : [],
        materialMiniProgram: res.attachments.materialMiniProgram.map((item) => {
          return {
            uid: '1',
            name: '微信小程序截图.jpg',
            url: typeof item === 'string' ? item : '',
          }
        }),
      },
    }
    console.log('mockData', mockData)
    Object.assign(formData, mockData)
  }
}

// 组件挂载时初始化
onMounted(() => {
  initFormData()
})
</script>

<template>
  <div class="user-model-form m-3">
    <div v-if="submitStatus" class="form-container">
      <div v-if="(formData?.verified === 0 && formData?.submitStatus === 0) || !id" class="title">
        {{ id ? '编辑' : '新建' }}经营主体
      </div>
      <a-alert v-if="formData?.verified === 3 && formData?.submitStatus === 0" message="已驳回"
        :description="`驳回原因：${formData?.verifiedComment}`" type="Warning" show-icon>
        <template #icon>
          <ExclamationCircleTwoTone style="color: #faad14;" />
        </template>
      </a-alert>

      <a-form ref="formRef" style="overflow-x: hidden;" class="h-[calc(100vh-400px)]" :model="formData"
        :rules="formRules" :label-col="{ span: 4 }" :wrapper-col="{ span: 24 }" layout="horizontal"
        :scroll-to-first-error="true">
        <!-- 经营主体信息 -->
        <a-card title="经营主体信息" class="form-section">
          <a-form-item label="经营主体名称" name="name">
            <a-input v-model:value="formData.name" placeholder="请输入经营主体名称" allow-clear />
          </a-form-item>

          <a-form-item label="经营简称" name="shortName">
            <a-input v-model:value="formData.shortName" placeholder="请输入经营简称" allow-clear />
          </a-form-item>

          <a-form-item label="主体类型" name="licenseType">
            <a-select v-model:value="formData.licenseType" placeholder="请选择主体类型" :options="LicenseTypeOptions" />
          </a-form-item>

          <a-form-item label="营业执照" :name="['attachments', 'businessLicensePhoto']">
            <a-row :gutter="16">
              <a-col :span="4">
                <a-upload v-model:file-list="formData.attachments.businessLicensePhoto" :headers="headers"
                  :max-size="5 * 1024 * 1024" accept=".jpg,.png" :max-count="1" list-type="picture-card"
                  :custom-request="handleCommonCustomRequest(formData.attachments.businessLicensePhoto, '营业执照', { maxSizeMB: 10, acceptTypes: ['jpg', 'jpeg', 'png'] })">
                  <div class="upload-button">
                    <plus-outlined />
                    <div style="margin-top: 8px">
                      点击上传图片
                    </div>
                  </div>
                </a-upload>
              </a-col>
              <a-col :span="12">
                <div class="upload-tips">
                  1、提供联系营业执照原件图片或彩色扫描，文字/盖章清晰可辨认<br>
                  2、文件大小不超过10M，格式支持: jpg、png<br>
                  3、上传营业执照后，系统自动识别关键信息，请注意核对
                </div>
              </a-col>
            </a-row>
          </a-form-item>

          <a-form-item label="统一社会信用代码" name="licenseNo">
            <a-input v-model:value="formData.licenseNo" placeholder="请输入统一社会信用代码" allow-clear />
          </a-form-item>

          <a-form-item label="证件有效期" required>
            <div style="display:inline-block;">
              <a-form-item style="margin-bottom:0;" name="licenseValidFrom" :rules="formRules.licenseValidFrom">
                <a-date-picker v-model:value="formData.licenseValidFrom" placeholder="开始日期" :format="dateFormat"
                  :value-format="dateFormat" style="width: 130px;" />
              </a-form-item>
            </div>
            <span class="mx-2">-</span>
            <div style="display:inline-block;">
              <a-form-item v-if="!formData.licenseLongTerm" style="margin-bottom:0;" name="licenseValidTo"
                :rules="formRules.licenseValidTo">
                <a-date-picker v-model:value="formData.licenseValidTo" placeholder="结束日期" :format="dateFormat"
                  :value-format="dateFormat" style="width: 130px;" />
              </a-form-item>
              <template v-else>
                <span style=" display: inline-block; width: 130px;color: #1890ff;">长期</span>
              </template>
            </div>
            <a-checkbox v-model:checked="formData.licenseLongTerm" class="ml-3" @change="handleLicenseLongTermChange">
              长期有效
            </a-checkbox>
          </a-form-item>

          <a-form-item label="注册资本" name="registerCapital">
            <a-input-number v-model:value="formData.registerCapital"
              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')" placeholder="请输入注册资本（元）" :min="0"
              :precision="2" style="width: 100%" />
          </a-form-item>

          <a-form-item label="注册地址" name="registerAddress">
            <a-input v-model:value="formData.registerAddress" placeholder="请输入注册地址（需和营业执照一致）" allow-clear />
          </a-form-item>

          <a-form-item label="经营范围" name="bizScope">
            <a-input v-model:value="formData.bizScope" placeholder="请输入经营范围" />
          </a-form-item>

          <a-form-item style="margin-bottom: -20px" label="经营地址" required>
            <a-row :gutter="[6, 0]" style="width: 60vw">
              <a-col :span="12">
                <a-form-item name="operatingAddress">
                  <a-cascader v-model:value="formData.operatingAddress"
                    :field-names="{ label: 'name', value: 'name', children: 'children' }" :options="addressOptions"
                    placeholder="请选择省市区" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item name="bizAddress">
                  <a-input v-model:value="formData.bizAddress" placeholder="请输入详细经营地址" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form-item>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="租赁协议">
                <a-upload v-model:file-list="formData.attachments.rentalAgreementFile" :max-size="10 * 1024 * 1024"
                  accept=".zip" :max-count="1" list-type="text"
                  :custom-request="handleCommonCustomRequest(formData.attachments.rentalAgreementFile, '租赁协议', { maxSizeMB: 10, acceptTypes: ['zip'] })">
                  <a-button>
                    <upload-outlined />
                    上传文件
                  </a-button>
                  <span class="ml-3">
                    若注册地址和经营地址不一致请提交租赁协议
                  </span>
                </a-upload>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 法人信息 -->
        <a-card title="法人信息" class="form-section">
          <a-form-item label="法人身份证正面" :name="['attachments', 'legalIdPhotoFront']">
            <a-row :gutter="16">
              <a-col :span="4">
                <a-upload v-model:file-list="formData.attachments.legalIdPhotoFront" :max-size="5 * 1024 * 1024"
                  accept=".jpg,.png" :max-count="1" list-type="picture-card"
                  :custom-request="handleCommonCustomRequest(formData.attachments.legalIdPhotoFront, '法人身份证正面', { maxSizeMB: 10, acceptTypes: ['jpg', 'jpeg', 'png'] })">
                  <div class="upload-button">
                    <plus-outlined />
                    <div style="margin-top: 8px">
                      点击上传图片
                    </div>
                  </div>
                </a-upload>
              </a-col>
              <a-col :span="12">
                <div class="upload-tips">
                  1、提供法人身份证原件图片或彩色扫描，文字/盖章清晰可辨认<br>
                  2、文件大小不超过10M，格式支持: jpg、png<br>
                  3、上传证件照片后，系统自动识别关键信息，请注意核对
                </div>
              </a-col>
            </a-row>
          </a-form-item>

          <a-form-item label="法人身份证反面" :name="['attachments', 'legalIdPhotoEnd']">
            <a-upload v-model:file-list="formData.attachments.legalIdPhotoEnd" :max-size="5 * 1024 * 1024"
              accept=".jpg,.png" :max-count="1" list-type="picture-card"
              :custom-request="handleCommonCustomRequest(formData.attachments.legalIdPhotoEnd, '法人身份证反面', { maxSizeMB: 10, acceptTypes: ['jpg', 'jpeg', 'png'] })">
              <div class="upload-button">
                <plus-outlined />
                <div style="margin-top: 8px">
                  点击上传图片
                </div>
              </div>
            </a-upload>
          </a-form-item>

          <a-form-item label="法人姓名" name="legalName">
            <a-input v-model:value="formData.legalName" placeholder="请输入法人姓名" allow-clear />
          </a-form-item>

          <a-form-item label="身份证号码" name="legalIdno">
            <a-input v-model:value="formData.legalIdno" placeholder="请输入身份证号码" allow-clear />
          </a-form-item>

          <a-form-item label="证件有效期" required>
            <div style="display:inline-block;">
              <a-form-item style="margin-bottom:0;" name="legalIdValidFrom" :rules="formRules.legalIdValidFrom">
                <a-date-picker v-model:value="formData.legalIdValidFrom" placeholder="开始日期" :format="dateFormat"
                  :value-format="dateFormat" style="width: 130px;" />
              </a-form-item>
            </div>
            <span class="mx-2">-</span>
            <div style="display:inline-block;">
              <a-form-item v-if="!formData.legalIdLongTerm" style="margin-bottom:0;" name="legalIdValidTo"
                :rules="formRules.legalIdValidTo">
                <a-date-picker v-model:value="formData.legalIdValidTo" placeholder="结束日期" :format="dateFormat"
                  :value-format="dateFormat" style="width: 130px;" />
              </a-form-item>
              <template v-else>
                <span style=" display: inline-block; width: 130px;color: #1890ff;">长期</span>
              </template>
            </div>

            <a-checkbox v-model:checked="formData.legalIdLongTerm" class="ml-3" @change="handleLegalIdLongTermChange">
              长期有效
            </a-checkbox>
          </a-form-item>

          <a-form-item label="手机号码" name="legalPhone">
            <a-input v-model:value="formData.legalPhone" placeholder="请输入手机号码" allow-clear />
          </a-form-item>

          <a-form-item label="法人联系详细地址" required>
            <a-row :gutter="[6, 0]" style="width: 60vw">
              <a-col :span="12">
                <a-form-item name="legalAddress">
                  <a-cascader v-model:value="formData.legalAddress"
                    :field-names="{ label: 'name', value: 'name', children: 'children' }" :options="addressOptions"
                    placeholder="请选择省市区" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item name="legalIdAddress">
                  <a-input v-model:value="formData.legalIdAddress" placeholder="请输入详细地址" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form-item>
        </a-card>

        <!-- 联系人信息 -->
        <a-card title="联系人信息" class="form-section">
          <a-form-item label="联系人是否与法人一致" name="contactSameAsLegal">
            <a-radio-group v-model:value="formData.contactSameAsLegal" @change="handleContactSameAsLegalChange">
              <a-radio :value="true">
                是
              </a-radio>
              <a-radio :value="false">
                否
              </a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="联系人邮箱" name="contactEmail">
            <a-input v-model:value="formData.contactEmail" placeholder="请输入邮箱" allow-clear />
          </a-form-item>

          <!-- 当联系人与法人不一致时显示 -->
          <template v-if="!formData.contactSameAsLegal">
            <a-form-item label="联系人身份证正面" :name="['attachments', 'contactPhotoFront']">
              <a-row :gutter="16">
                <a-col :span="4">
                  <a-upload v-model:file-list="formData.attachments.contactPhotoFront" :max-size="5 * 1024 * 1024"
                    accept=".jpg,.png" :max-count="1" list-type="picture-card"
                    :custom-request="handleCommonCustomRequest(formData.attachments.contactPhotoFront, '联系人身份证正面', { maxSizeMB: 10, acceptTypes: ['jpg', 'jpeg', 'png'] })">
                    <div class="upload-button">
                      <plus-outlined />
                      <div style="margin-top: 8px">
                        点击上传图片
                      </div>
                    </div>
                  </a-upload>
                </a-col>
                <a-col :span="12">
                  <div class="upload-tips">
                    1、提供联系人身份证原件图片或彩色扫描，文字/盖章清晰可辨认<br>
                    2、文件大小不超过10M，格式支持: jpg、png<br>
                    3、上传证件照片后，系统自动识别关键信息，请注意核对
                  </div>
                </a-col>
              </a-row>
            </a-form-item>
            <a-form-item label="联系人身份证反面" :name="['attachments', 'contactPhotoEnd']">
              <a-upload v-model:file-list="formData.attachments.contactPhotoEnd" :max-size="5 * 1024 * 1024"
                accept=".jpg,.png " :max-count="1" list-type="picture-card"
                :custom-request="handleCommonCustomRequest(formData.attachments.contactPhotoEnd, '联系人身份证反面', { maxSizeMB: 10, acceptTypes: ['jpg', 'jpeg', 'png'] })">
                <div class="upload-button">
                  <plus-outlined />
                  <div style="margin-top: 8px">
                    点击上传图片
                  </div>
                </div>
              </a-upload>
            </a-form-item>

            <a-form-item label="联系人姓名" name="contactName">
              <a-input v-model:value="formData.contactName" placeholder="请输入联系人姓名" allow-clear />
            </a-form-item>

            <a-form-item label="联系人手机号" name="contactPhone">
              <a-input v-model:value="formData.contactPhone" placeholder="请输入手机号码" allow-clear />
            </a-form-item>

            <a-form-item label="身份证号码" name="contactIdno">
              <a-input v-model:value="formData.contactIdno" placeholder="请输入身份证号码" allow-clear />
            </a-form-item>

            <a-form-item label="证件有效期" required>
              <div style="display:inline-block;">
                <a-form-item style="margin-bottom:0;" name="contactIdValidFrom">
                  <a-date-picker v-model:value="formData.contactIdValidFrom" placeholder="开始日期" :format="dateFormat"
                    :value-format="dateFormat" style="width: 130px;" />
                </a-form-item>
              </div>
              <span class="mx-2">-</span>
              <div style="display:inline-block;">
                <a-form-item v-if="!formData.contactIdLongTerm" style="margin-bottom:0;" name="contactIdValidTo"
                  :rules="formRules.contactIdValidTo">
                  <a-date-picker v-model:value="formData.contactIdValidTo" placeholder="结束日期" :format="dateFormat"
                    :value-format="dateFormat" style="width: 130px;" />
                </a-form-item>
                <template v-else>
                  <span style=" display: inline-block; width: 130px;color: #1890ff;">长期</span>
                </template>
              </div>
              <a-checkbox v-model:checked="formData.contactIdLongTerm" class="ml-3"
                @change="handleContactIdLongTermChange">
                长期有效
              </a-checkbox>
            </a-form-item>

            <a-form-item label="业务办理授权函" :name="['attachments', 'bizAuthorizationLetterFile']">
              <a-row :gutter="16">
                <a-col :span="4">
                  <a-upload v-model:file-list="formData.attachments.bizAuthorizationLetterFile" :max-count="1"
                    accept=".rar,.zip" list-type="text"
                    :custom-request="handleCommonCustomRequest(formData.attachments.bizAuthorizationLetterFile, '业务办理授权函', { maxSizeMB: 50, acceptTypes: ['rar', 'zip'] })">
                    <a-button>
                      <upload-outlined />
                      上传文件
                    </a-button>
                  </a-upload>
                </a-col>
                <a-col :span="12">
                  <div class="upload-tips">
                    1、提供业务办理授权函的文件，点击下方"下载模板"即可下载业务办理授权函的模板<br>
                    2、文件大小不超过50M<br>
                  </div>
                  <a-button type="link" @click="downloadTemplate">
                    下载模板
                  </a-button>
                </a-col>
              </a-row>
            </a-form-item>
          </template>
        </a-card>

        <!-- 受益人信息 -->
        <a-card title="受益人信息" class="form-section">
          <div class="beneficiary-header">
            <a-alert message="若除法人外还有其余受益人信息，请点击右侧添加按钮添加" type="info" show-icon />
            <a-button v-if="formData.beneficiaries.length < 1" type="primary" @click="addBeneficiary">
              <PlusOutlined />
              添加更多受益人
            </a-button>
          </div>

          <div v-for="(beneficiary, index) in formData.beneficiaries" :key="beneficiary._key" class="beneficiary-item">
            <a-form-item label="受益人身份证正面" :name="['beneficiaries', index, 'beneficiaryIdCardFront']" :rules="{
              required: true,
              message: '请上传受益人身份证正面',
            }">
              <a-row :gutter="16">
                <a-col :span="4">
                  <a-upload v-model:file-list="beneficiary.beneficiaryIdCardFront" :max-size="5 * 1024 * 1024"
                    accept=".jpg,.png" :max-count="1" list-type="picture-card"
                    :custom-request="handleCommonCustomRequest(beneficiary.beneficiaryIdCardFront, '受益人身份证正面', { maxSizeMB: 10, acceptTypes: ['jpg', 'jpeg', 'png'] })">
                    <div class="upload-button">
                      <plus-outlined />
                      <div style="margin-top: 8px">
                        点击上传图片
                      </div>
                    </div>
                  </a-upload>
                </a-col>

                <a-col :span="12">
                  <div class="upload-tips">
                    1、提供受益人身份证原件照片或彩色扫描，文字/盖章清晰可辨认<br>
                    2、文件大小不超过10M，格式支持：jpg、png<br>
                    3、上传证件照片后，系统自动识别关键信息，请注意核对
                  </div>
                </a-col>
              </a-row>
            </a-form-item>

            <a-form-item label="受益人身份证反面" :name="['beneficiaries', index, 'beneficiaryIdCardBack']" :rules="{
              required: true,
              message: '请上传受益人身份证反面',
            }">
              <a-upload v-model:file-list="beneficiary.beneficiaryIdCardBack" :max-size="5 * 1024 * 1024"
                accept=".jpg,.png" :max-count="1" list-type="picture-card"
                :custom-request="handleCommonCustomRequest(beneficiary.beneficiaryIdCardBack, '受益人身份证反面', { maxSizeMB: 10, acceptTypes: ['jpg', 'jpeg', 'png'] })">
                <div class="upload-button">
                  <plus-outlined />
                  <div style="margin-top: 8px">
                    点击上传图片
                  </div>
                </div>
              </a-upload>
            </a-form-item>

            <a-form-item label="受益人姓名" :name="['beneficiaries', index, 'beneficiaryName']" :rules="{
              required: true,
              message: '请输入受益人姓名',
            }">
              <a-input v-model:value="beneficiary.beneficiaryName" placeholder="请输入受益人姓名" allow-clear />
            </a-form-item>

            <a-form-item label="身份证号码" :name="['beneficiaries', index, 'beneficiaryIdNumber']" :rules="[
              { required: true, message: '请输入身份证号码' },
              { pattern: /^\d{17}(\d|X|x)$|^\d{15}$/, message: '请输入有效的身份证号码' },
            ]">
              <a-input v-model:value="beneficiary.beneficiaryIdNumber" placeholder="请输入身份证号码" allow-clear />
            </a-form-item>

            <!-- 受益人证件有效期表单项一行展示 -->
            <a-form-item label="证件有效期" :required="true">
              <div style="display:inline-block;">
                <a-form-item style="margin-bottom:0;" :name="['beneficiaries', index, 'certificateStartDate']"
                  :rules="[{ required: true, message: '请选择证件开始日期', trigger: 'change' }]">
                  <a-date-picker v-model:value="beneficiary.certificateStartDate" placeholder="开始日期"
                    :format="dateFormat" :value-format="dateFormat" style="width: 120px;" />
                </a-form-item>
              </div>
              <span class="mx-2">-</span>
              <div style="display:inline-block;">
                <a-form-item v-if="!beneficiary.idLongTerm" style="margin-bottom:0;"
                  :name="['beneficiaries', index, 'certificateEndDate']" :rules="[
                    { required: true, message: '请选择证件结束日期', trigger: 'change' },
                    { validator: (_rule, value) => { if (value && beneficiary.certificateStartDate && value < beneficiary.certificateStartDate) { return Promise.reject(new Error('结束日期不能早于开始日期')) } return Promise.resolve() }, trigger: 'change' },
                             ]">
                  <a-date-picker v-model:value="beneficiary.certificateEndDate" placeholder="结束日期" :format="dateFormat"
                    :value-format="dateFormat" style="width: 120px;" />
                </a-form-item>
                <template v-else>
                  <span style=" display: inline-block; width: 120px;color: #1890ff;">长期</span>
                </template>
              </div>
              <a-checkbox v-model:checked="beneficiary.idLongTerm" class="ml-3"
                @change="(e) => handleBeneficiaryLongTermChange(beneficiary, e.target.checked, index)">
                长期有效
              </a-checkbox>
            </a-form-item>

            <a-form-item label="受益人联系详细地址" required>
              <a-row :gutter="[6, 0]" style="width: 60vw">
                <a-col :span="12">
                  <a-form-item :name="['beneficiaries', index, 'address']" :rules="{
                    required: true,
                    message: '请选择省市区',
                  }">
                    <a-cascader v-model:value="beneficiary.address"
                      :field-names="{ label: 'name', value: 'name', children: 'children' }" :options="addressOptions"
                      placeholder="请选择省市区" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :name="['beneficiaries', index, 'beneficiaryContactAddress']" :rules="{
                    required: true,
                    message: '请输入详细经营地址',
                  }">
                    <a-input v-model:value="beneficiary.beneficiaryContactAddress" placeholder="请输入详细经营地址"
                      allow-clear />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form-item>

            <div class="flex justify-end">
              <a-button type="link" danger @click="removeBeneficiary(index)">
                删除此受益人
              </a-button>
            </div>
          </div>

          <div class="flex justify-end">
            <a-button v-if="formData.beneficiaries.length >= 1" :disabled="formData.beneficiaries.length >= 3" danger
              @click="addBeneficiary">
              <PlusOutlined />
              添加更多受益人
            </a-button>
          </div>
        </a-card>

        <!-- 公户信息 -->
        <a-card title="公户信息" class="form-section">
          <a-form-item label="证明文件" :name="['bankAccount', 'attachments']">
            <a-upload v-model:file-list="formData.bankAccount.attachments" :max-size="5 * 1024 * 1024"
              accept=".jpg,.png" :max-count="1" list-type="picture-card"
              :custom-request="handleCommonCustomRequest(formData.bankAccount.attachments, '证明文件', { maxSizeMB: 10, acceptTypes: ['jpg', 'jpeg', 'png'] })">
              <div class="upload-button">
                <plus-outlined />
                <div style="margin-top: 8px">
                  点击上传图片
                </div>
              </div>
            </a-upload>
            <!-- 示例图片 -->
            <div class="ml[-50px] flex flex-row items-center gap-10px">
              示例：
              <a-image :width="75" src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" />
              <a-image :width="75" src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" />
              <a-image :width="75" src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" />
              <div class="upload-tips">
                1、需要从开户许可证、印鉴卡、打印银行资料盖章中选择一份上传，文字/盖章清晰可辨认<br>
                2、仅需上传一张图片<br>
                3、文件大小不超过10M，格式支持：jpg、png
              </div>
            </div>
          </a-form-item>
          <a-form-item label="账户名" :name="['bankAccount', 'accountName']">
            <a-input v-model:value="formData.bankAccount.accountName" placeholder="请输入注册账户名" allow-clear />
          </a-form-item>
          <a-form-item label="注册账户号" :name="['bankAccount', 'accountNo']">
            <a-input v-model:value="formData.bankAccount.accountNo" placeholder="请输入注册账户号" allow-clear />
          </a-form-item>
          <a-form-item label="开户银行" :name="['bankAccount', 'bankName']">
            <a-select v-model:value="formData.bankAccount.bankName" placeholder="请选择开户银行" allow-clear
              :options="BankNameOptions" @change="changeOpenBankCity" />
          </a-form-item>
          <a-form-item label="开户城市" :name="['bankAccount', 'openBankCityStr']">
            <a-cascader v-model:value="formData.bankAccount.openBankCityStr" :options="addressOptions"
              placeholder="请选择开户城市" :field-names="{ label: 'name', value: 'name' }" :show-search="false" allow-clear
              :display-render="handleRenderDisplay" @change="changeOpenBankCity" />
          </a-form-item>

          <a-form-item label="开户支行" :name="['bankAccount', 'bankBranchName']">
            <a-select v-model:value="formData.bankAccount.bankBranchName" placeholder="请选择开户支行"
              :options="subBankOptions" show-search allow-clear />
          </a-form-item>

          <a-form-item label="支行银联号" :name="['bankAccount', 'openBankCode']">
            <a-input v-model:value="formData.bankAccount.openBankCode" placeholder="请输入支行银联号" allow-clear />
          </a-form-item>
        </a-card>

        <!-- 其他信息 -->
        <a-card title="其他信息" class="form-section">
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="微信小程序截图" :name="['attachments', 'materialMiniProgram']">
                <a-upload v-model:file-list="formData.attachments.materialMiniProgram" :max-size="5 * 1024 * 1024"
                  accept=".jpg,.png" :max-count="3" list-type="picture-card"
                  :custom-request="handleCommonCustomRequest(formData.attachments.materialMiniProgram, '微信小程序截图', { maxSizeMB: 10, acceptTypes: ['jpg', 'jpeg', 'png'] })">
                  <div class="upload-button">
                    <plus-outlined />
                    <div style="margin-top: 8px">
                      点击上传图片
                    </div>
                  </div>
                </a-upload>

                <!-- 示例图片 -->
                <div class="ml[-50px] flex flex-row items-center gap-10px">
                  示例：
                  <a-image :width="75"
                    src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" />
                  <a-image :width="75"
                    src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" />

                  <div class="upload-tips">
                    1、提供包括：名称页、主体页<br>
                    2、文件大小不超过10M，格式支持: jpg、png
                  </div>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
      </a-form>
      <!-- 表单操作按钮 -->
      <div class="form-actions">
        <a-space>
          <!-- 再次确认，参数 -->
          <a-button type="primary" :loading="submitting" @click="submitForm">
            提交
          </a-button>
          <a-button @click="saveDraft">
            暂存
          </a-button>
          <a-button type="primary" @click="resetForm">
            取消
          </a-button>
        </a-space>
      </div>
    </div>

    <div v-else class="form-container p-16px">
      <div class="statesBox">
        <div class="flex items-center gap-2">
          <div class="name">
            {{ viewFormData?.name }}
          </div>
        </div>
        <div class="mr-20px flex items-center gap-2">
          <!-- 二次确认 -->
          <a-button type="primary" :loading="submitting" @click="submitConfirm">
            提交
          </a-button>
          <a-button @click="saveDraft">
            暂存
          </a-button>
          <a-button type="primary" @click="submitStatus = true">
            取消
          </a-button>
        </div>
      </div>
      <BaseInfo :data="viewFormData" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.user-model-form {
  min-height: 75vh;
  background: #f5f5f5;

  .form-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

    .title {
      padding: 24px;
      font-size: 18px;
      font-weight: 600;
    }

    .statesBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .name {
        font-size: 18px;
        font-weight: bold;
      }

      .states {
        font-size: 16px;
        font-weight: bold;
        color: red;
      }

    }
  }

  .form-hint {
    margin-top: 8px;
    font-size: 12px;
    line-height: 2;
    color: #666;

    .ant-tag {
      margin-right: 4px;
    }
  }

  .upload-tips {
    padding: 8px;
    margin-top: 8px;
    font-size: 13px;
    line-height: 2;
    color: #666;
  }

  .beneficiary-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .beneficiary-item {
    padding: 16px;
    margin-bottom: 16px;
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
  }

  .form-actions {
    padding: 24px 0;
    margin-top: 24px;
    text-align: center;
    border-top: 1px solid #e8e8e8;
  }

  :deep(.ant-form-item-label) {
    font-weight: 500;
  }

  :deep(.ant-upload-list-picture-card .ant-upload-list-item) {
    width: 104px;
    height: 104px;
  }

  :deep(.ant-upload.ant-upload-select-picture-card) {
    width: 104px;
    height: 104px;
    background: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    transition: border-color 0.3s;

    &:hover {
      border-color: #1890ff;
    }
  }
}
</style>
