<script lang="ts" setup>
import { h } from 'vue'
import { Button, Image } from 'ant-design-vue'
import { Description, useDescription } from '@/components/Description/index'

defineOptions({ name: 'CorporateInfo' })

const props = defineProps<{ data: Record<string, any> }>()
const data = ref(props.data)

watch(
  () => props.data,
  (val) => {
    data.value = val
  },
  { immediate: true },
)

const [descriptionRegister] = useDescription({
  column: 3,
  schema: [
    {
      field: 'contactName',
      label: '联系人姓名',
    },
    {
      field: 'contactPhone',
      label: '联系人手机号',
    },
    {
      field: 'contactIdno',
      label: '身份证号',
    },
    {
      field: 'creditCode',
      label: '身份证有效期',
      render: () => {
        return `${data.value?.contactIdValidFrom} 至 ${data.value?.contactIdValidTo || '长期'}`
      },
    },
    {
      field: 'templateParams',
      label: '身份证人像面',
      render() {
        return h(Image, {
          src: data.value?.attachments?.contactPhotoFront,
          width: '150px',
        })
      },
    },
    {
      field: 'templateParams',
      label: '身份证国徽面',
      render() {
        return h(Image, {
          src: data.value?.attachments?.contactPhotoEnd,
          width: '150px',
        })
      },
    },
    {
      field: 'contactEmail',
      label: '联系人邮箱',
    },
    {
      field: 'templateParams',
      label: '业务办理授权函',
      render() {
        const val = data.value?.attachments?.bizAuthorizationLetterFile
        if (!val)
          return '暂无业务办理授权函'
        return h(Button, {
          href: val,
          target: '_blank',
        }, `业务办理授权函.${val?.split('.')?.pop() || ''}`)
      },
    },
  ],
  data,
})
</script>

<template>
  <Description title="联系人信息" @register="descriptionRegister" />
</template>
