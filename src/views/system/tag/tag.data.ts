import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

export const columns: BasicColumn[] = [
  {
    title: '编号',
    dataIndex: 'id',
    defaultHidden: true,
    width: 160
  },
  {
    title: '标签类型',
    dataIndex: 'type',
    width: 160,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.CLUB_TAG_TYPE)
    },
  },
  {
    title: '标签名称',
    dataIndex: 'name',
    width: 160
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '标签类型',
    field: 'type',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.CLUB_TAG_TYPE),
    },
    colProps: { span: 8 }
  },
  {
    label: '标签名称',
    field: 'name',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
    colProps: { span: 8 }
  },
]

export const createFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input'
  },
  {
    label: '标签类型',
    field: 'type',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.CLUB_TAG_TYPE),
    },
  },
  {
    label: '标签名称',
    field: 'name',
    required: true,
    component: 'Input'
  },
]

export const updateFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input'
  },
  {
    label: '标签类型',
    field: 'type',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.CLUB_TAG_TYPE, 'string'),
    },
  },
  {
    label: '标签名称',
    field: 'name',
    required: true,
    component: 'Input'
  },
]