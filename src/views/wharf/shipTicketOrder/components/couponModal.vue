<script lang="ts" setup>
import { ref, } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { BasicModal, useModalInner, useModal } from '@/components/Modal'
import { BasicTable, TableAction, useRender, useTable } from '@/components/Table'
import { couponList } from '@/api/mall/trade/hotel'
import { DICT_TYPE } from '@/utils/dict'

defineOptions({ name: 'CombinationRecordModal' })
const id = ref('');
const price = ref(0)
const emit = defineEmits(['success', 'register'])

const { t } = useI18n()

const [registerModal, { setModalProps, closeModal }] = useModalInner((data) => {
  setModalProps({ loading: true });
  id.value = data.id;
  price.value = data.price;
  setModalProps({ loading: false });
});

const [registerTable] = useTable({
  api: query => couponList(id.value),
  columns: [
    {
      title: '优惠券名称', dataIndex: 'name', ellipsis: true
    }
    ,
    {
      title: '优惠券类型', dataIndex: 'productScope',
      customRender: ({ record }) => {
        return useRender.renderDict(record.discountType, DICT_TYPE.PROMOTION_PRODUCT_SCOPE)
      }
    },
    {
      title: '优惠类型', dataIndex: 'discountType',
      customRender: ({ record }) => {
        return useRender.renderDict(record.discountType, DICT_TYPE.PROMOTION_DISCOUNT_TYPE)
      }
    },
    {
      title: '优惠', dataIndex: 'discountPrice',
      customRender: ({ record }) => {
        if (record.discountType === 1) {
          return record.discountPrice && '¥ ' + (record.discountPrice / 100).toFixed(2) + '元';
        } else if (record.discountType === 2) {
          return record.discountPercent && record.discountPercent + '%';
        }
      }
    },
    {
      title: '优惠金额', dataIndex: 'discountPrice',
      customRender: ({ record }) => {
        return price.value ? '¥ ' + (price.value / 100).toFixed(2) + '元' : '¥ 0元';
      }
    }
  ],
  useSearchForm: false,
  showTableSetting: false,
  showIndexColumn: false
})
</script>

<template>
  <BasicModal v-bind="$attrs" title="商家优惠券使用情况" @register="registerModal" :showOkBtn="false" :showCancelBtn="false">
    <BasicTable @register="registerTable"></BasicTable>
  </BasicModal>
</template>