<script lang="ts" setup>
import PointProductCatsModal from './PointProductCatsModal.vue'
import { columns, searchFormSchema } from './pointProductCats.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import {
  deletePointProductCats,
  exportPointProductCats,
  getPointProductCatsPage,
  updatePointProductCats
} from '@/api/mall/pointsMall/pointsProductCategories'

defineOptions({ name: 'PointProductCats' })

const { t } = useI18n()
const { createConfirm, createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const [registerTable, { getForm, reload }] = useTable({
  title: '商品类别列表',
  api:  async (params) => {
    const res = await getPointProductCatsPage(params);
    console.log(res, 'res');
    // 对数据按 sort 从小到大排序
    // return res.list.sort((a, b) => a.sort - b.sort);
    return res.list
  },
    columns,
    formConfig: { labelWidth: 120, schemas: searchFormSchema },
    useSearchForm: true,
    showTableSetting: true,
    actionColumn: {
      width: 140,
      title: t('common.action'),
      dataIndex: 'action',
      fixed: 'right',
    },
})


function handleCreate() {
  openModal(true, { isUpdate: false })
}

function handleEdit(record: Recordable) {
  openModal(true, { record, isUpdate: true })
}



async function handleExport() {
  createConfirm({
    title: t('common.exportTitle'),
    iconType: 'warning',
    content: t('common.exportMessage'),
    async onOk() {
      await exportPointProductCats(getForm().getFieldsValue())
      createMessage.success(t('common.exportSuccessText'))
    },
  })
}

async function handleDelete(record: Recordable) {
  await deletePointProductCats(record.id)
  createMessage.success(t('common.delSuccessText'))
  reload()
}

async function handleStatus(record: Recordable) {
  record.status = record.status == 0 ? 1 : 0

  await updatePointProductCats(record).then(res=>{if(res){
    createMessage.success(record.status == 0 ?"发布成功":"取消发布成功")
  }})





  reload()
}



</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" v-auth="['pointMall:point-product-cats:create']" :preIcon="IconEnum.ADD" @click="handleCreate">
          {{ t('action.create') }}
        </a-button>
        <a-button v-auth="['pointMall:point-product-cats:export']" :preIcon="IconEnum.EXPORT" @click="handleExport">
          {{ t('action.export') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              { icon: IconEnum.EDIT, label: t('action.edit'), auth: 'pointMall:point-product-cats:update', onClick: handleEdit.bind(null, record) },
              {
                icon: IconEnum.DELETE,
                danger: true,
                label: t('action.delete'),
                auth: 'pointMall:point-product-cats:delete',
                popConfirm: {
                  title: t('common.delMessage'),
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
              {
                label: '取消发布',
                danger: true,
                ifShow: record.status == 0,
                auth: 'pointMall:point-product-cats:update',
                // onClick: handleStatus.bind(null, record)
                popConfirm: {
                  title: '确认要取消发布吗',
                  placement: 'left',
                  confirm: handleStatus.bind(null, record),
                },
              },
              {
                label: '发布',
                ifShow: record.status == 1,
                auth: 'pointMall:point-product-cats:update',
                onClick: handleStatus.bind(null, record)

              }
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PointProductCatsModal @register="registerModal" @success="reload()" />
  </div>
</template>
