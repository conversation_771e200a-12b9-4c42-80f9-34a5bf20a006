<script lang="ts" setup>
import PointProductOrderModal from './PointProductOrderModal.vue'
import PointProductDetailModal  from '@/views/mall/pointsMall/pointsProductOrders/order/PointProductDetailModal.vue';
import { columns, searchFormSchema } from './pointProductOrder.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { exportPointProductOrder, getPointProductOrderPage } from '@/api/mall/pointsMall/pointsProductOrders/order'

defineOptions({ name: 'PointProductOrder' })

const { t } = useI18n()
const { createConfirm, createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
// 为详情弹窗注册一个新的 Modal 控制器
const [registerDetailModal, { openModal: openDetailModal }] = useModal()
const [registerTable, { getForm, reload }] = useTable({
  title: '积分商城订单列表',
  api: getPointProductOrderPage,
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showIndexColumn: false,
  rowKey: 'id', // 指定行的唯一键
  showTableSetting: true,
  actionColumn: {
    width: 140,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  },
})

function handleCreate() {
  openModal(true, { isUpdate: false })
}

function handleEdit(record: Recordable) {
  openModal(true, { record, isUpdate: true })
}

function handleDetail(record: Recordable) {
  // 使用新的 openDetailModal 打开详情弹窗
  openDetailModal(true, { record, isDetail: true })
}

async function handleExport() {
  createConfirm({
    title: t('common.exportTitle'),
    iconType: 'warning',
    content: t('common.exportMessage'),
    async onOk() {
      await exportPointProductOrder(getForm().getFieldsValue())
      createMessage.success(t('common.exportSuccessText'))
    },
  })
}

</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" v-auth="['pointMall:point-product-order:create']" :preIcon="IconEnum.ADD" @click="handleCreate">
          {{ t('action.create') }}
        </a-button>
        <a-button v-auth="['pointMall:point-product-order:export']" :preIcon="IconEnum.EXPORT" @click="handleExport">
          {{ t('action.export') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: IconEnum.VIEW, // 使用查看图标，您可能需要确认项目中有这个图标，如果没有可以使用 EYE 或类似图标
                label: t('action.detail'),
                // auth: 'pointMall:point-product-order:view', // 根据实际权限调整
                onClick: handleDetail.bind(null, record),
              },
              { icon: IconEnum.EDIT, label: t('action.edit'), auth: 'pointMall:point-product-order:update', onClick: handleEdit.bind(null, record) },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PointProductOrderModal @register="registerModal" @success="reload()" />
    <!-- 添加新的详情弹窗组件 -->
    <PointProductDetailModal @register="registerDetailModal" />
  </div>
</template>
