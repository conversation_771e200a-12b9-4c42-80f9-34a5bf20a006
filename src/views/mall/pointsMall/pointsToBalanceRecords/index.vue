<script lang="ts" setup>
import { columns, searchFormSchema } from './pointsToBalanceRecords.data'
import PointsToBalanceRecordForm from './PointsToBalanceRecordForm.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { IconEnum } from '@/enums/appEnum'
import {
  getPointsToBalanceRecordPage,
} from '@/api/mall/pointsMall/pointsToBalanceRecords'

defineOptions({ name: 'PointsToBalanceRecords' })
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
const [registerTable] = useTable({
  title: '积分转余额记录',
  api: getPointsToBalanceRecordPage,
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  rowKey: 'id', // 指定行的唯一键
  actionColumn: {
    width: 80,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
       
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EYE,
              label: '查看',
              onClick: () => openModal(true, { ...record }),
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <PointsToBalanceRecordForm @register="registerModal" />
  </div>
</template>
