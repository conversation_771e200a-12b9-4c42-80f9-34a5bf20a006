<script lang="ts" setup>
import { ref, unref } from 'vue'
import { createFormSchema, updateFormSchema } from './pointProductSkus.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { BasicForm, useForm } from '@/components/Form'
import { BasicModal, useModalInner } from '@/components/Modal'
import { createPointProductSkus, getPointProductSkus, updatePointProductSkus } from '@/api/mall/pointsMall/pointsProductManagement/skus'

defineOptions({ name: 'PointProductSkusModal' })

const emit = defineEmits(['success', 'register'])

const { t } = useI18n()
const { createMessage } = useMessage()
const isUpdate = ref(true)

const [registerForm, { setFieldsValue, resetFields, resetSchema, validate }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: createFormSchema,
  showActionButtonGroup: false,
  actionColOptions: { span: 23 },
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields()
  setModalProps({
    confirmLoading: false,
    // bodyStyle: {
    //   maxHeight: '600px',
    //   overflowY: 'auto',
    //   marginTop:"20px",
    //   marginBottom:"20px",
    //   marginRight: "10px",
    // }

  })
  isUpdate.value = !!data?.isUpdate
  if (unref(isUpdate)) {
    resetSchema(updateFormSchema)
    const res = await getPointProductSkus(data.record.id)
    setFieldsValue({ ...res })
  }else
    resetSchema(createFormSchema)
})

async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true })
    if (values.productImg && typeof values.productImg === 'string') {
      values.productImg = values.productImg.split(',')
    }
    if (unref(isUpdate)){

      await updatePointProductSkus(values)

    }

    else
    {
      await createPointProductSkus(values)
    }


    closeModal()
    emit('success')
    createMessage.success(t('common.saveSuccessText'))
  } finally {
    setModalProps({ confirmLoading: false })
  }
}
</script>
<template>
  <BasicModal v-bind="$attrs" :title="isUpdate ? t('action.edit') : t('action.create')" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
