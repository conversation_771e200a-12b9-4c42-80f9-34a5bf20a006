<script lang="ts" setup>
import PointProductSkusModal from './PointProductSkusModal.vue'
import {columns, getNeedPostage, searchFormSchema, setNeedPostage} from './pointProductSkus.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import {
  deletePointProductSkus,
  exportPointProductSkus,
  getPointProductSkusPage,
  updatePointProductSkus
} from '@/api/mall/pointsMall/pointsProductManagement/skus'
import {getSpuForSku} from "@/views/mall/pointsMall/pointsProductManagement/pointProductSpus/index.vue";
import {getPointProductSpusPage} from "@/api/mall/pointsMall/pointsProductManagement/spus";

defineOptions({ name: 'PointProductSkus' })
let loadTimes=0
// const props = defineProps<{
//   spuData?: any
// }>()
const props = defineProps<{
  spuId?: number | null
}>()
watch(() => props.spuId, (newSpuId) => {
  //避免未渲染时无法reload
  if (loadTimes>0) {
  if (newSpuId) {
    reload()
  }
  }
  loadTimes++
}, { immediate: true })



const { t } = useI18n()
const { createConfirm, createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const [registerTable, { getForm, reload }] = useTable({
  title: '商品规格列表',
  api: async (params)=>{
    const res = await getPointProductSkusPage(params);
    // return res.list.sort((a, b) => a.sort - b.sort);
    return res.list
  },
    columns,
    formConfig: { labelWidth: 120, schemas: searchFormSchema },
    useSearchForm: true,
    showIndexColumn: false,
    rowKey: 'id', // 指定行的唯一键
    showTableSetting: true,
    actionColumn: {
      width: 200,
      title: t('common.action'),
      dataIndex: 'action',
      fixed: 'right',
    },
})

function handleCreate() {
  console.log(getSpuForSku().logisticsType)
  setNeedPostage(getSpuForSku().logisticsType===2)
  console.log(getNeedPostage())
  openModal(true, { isUpdate: false })
}

function handleEdit(record: Recordable) {
  console.log(getSpuForSku().logisticsType)
  setNeedPostage(getSpuForSku().logisticsType===2)
  console.log(getNeedPostage())
  openModal(true, { record, isUpdate: true })
}

async function handleStatus(record: Recordable){
  record.putaway = record.putaway == 0 ? 1 : 0
  await updatePointProductSkus(record).then(res=>{
    if(res){
      createMessage.success(record.putaway == 0 ?"发布成功":"取消发布成功")
    }
  })
  reload()
}

async function handleExport() {
  createConfirm({
    title: t('common.exportTitle'),
    iconType: 'warning',
    content: t('common.exportMessage'),
    async onOk() {
      await exportPointProductSkus(getForm().getFieldsValue())
      createMessage.success(t('common.exportSuccessText'))
    },
  })
}

async function handleDelete(record: Recordable) {
  await deletePointProductSkus(record.id)
  createMessage.success(t('common.delSuccessText'))
  reload()
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary"  :preIcon="IconEnum.ADD" @click="handleCreate">
          {{ t('action.create') }}
        </a-button>
        <a-button  :preIcon="IconEnum.EXPORT" @click="handleExport">
          {{ t('action.export') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              { icon: IconEnum.EDIT, label: t('action.edit'), onClick: handleEdit.bind(null, record) },
              {
                icon: IconEnum.DELETE,
                danger: true,
                label: t('action.delete'),

                popConfirm: {
                  title: t('common.delMessage'),
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
              {
                  label:'发布',
                  ifShow: record.putaway === 1,
                  // auth:'pointMall:point-product-spus:update',
                  onClick: handleStatus.bind(null, record)
              },
              {
                  label:'取消发布',
                  danger: true,
                  ifShow: record.putaway === 0,
                  // auth:'pointMall:point-product-spus:update',
                  popConfirm: {
                  title: '确认要取消发布吗',
                  placement: 'left',
                  confirm: handleStatus.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PointProductSkusModal @register="registerModal" @success="reload()" />
  </div>
</template>
