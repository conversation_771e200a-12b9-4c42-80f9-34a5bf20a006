import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'
import {DICT_TYPE, getDictOptions} from "@/utils/dict";
import {getSpuForSku} from "@/views/mall/pointsMall/pointsProductManagement/pointProductSpus/index.vue";




export const columns: BasicColumn[] = [
  {
    title: '商品ID',
    dataIndex: 'spuId',
    width: 160,
    ifShow:false,
  },
  {
    title: '规格名称',
    dataIndex: 'name',
    width: 160,
  },
  {
    title: '封面图',
    dataIndex: 'productImg',
    width: 110,
    customRender: ({ text }) => {
      if (!text) return ''
      return h('div', {
        style: {
          height: '100px',
          width: '100px',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }
      }, [
        h('img', {
          src: text,
          style: {
            height: '100px',
            width: '100px',
            objectFit: 'cover',
            objectPosition: 'center',
          }
        })
      ])
    }

  },
  {
    title: '所需积分',
    dataIndex: 'points',
    width: 160,
  },
  {
    title: '原价',
    dataIndex: 'parPrice',
    width: 160,
  },
  {
    title: '库存',
    dataIndex: 'stocks',
    width: 160,
  },
  {
    title: '销量',
    dataIndex: 'salesCount',
    width: 160,
  },
  {
    title: '邮费',
    dataIndex: 'postage',
    width: 160,
  },
  {
    title: '发布状态',
    dataIndex: 'putaway',
    width: 160,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.POINT_MALL_CATS_STATUS)
    },
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 160,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    },
  },
]

export const searchFormSchema: FormSchema[] = [
  // {
  //   label: '商品ID',
  //   field: 'spuId',
  //   component: 'Input',
  //   colProps: { span: 8 },
  // },
  {
    label: '规格名称',
    field: 'name',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '发布状态',
    field: 'putaway',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_MALL_CATS_STATUS, 'number'),
    },
    colProps: { span: 8 },
  },
]
const needPostage=ref(true)
export const createFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input',
  },
  {
    label: '商品ID',
    field: 'spuId',
    required: false,
    component: 'Input',
    ifShow:false,

  },
  {
    label: '规格名称',
    field: 'name',
    required: true,
    component: 'Input',
  },
  {
    label: '规格封面',
    field: 'productImg',
    required: true,
    component: 'FileUpload',
    componentProps: {
      maxCount: 1,
      fileType: 'image',
      hintText: '图片大小不超过4M'
    },
  },
  {
    label: '原价',
    field: 'parPrice',
    required: true,
    component: 'Input',
  },
  {
    label: '所需积分',
    field: 'points',
    required: true,
    component: 'Input',
  },
  {
    label: '库存',
    field: 'stocks',
    required: true,
    component: 'Input',
  },
  {
    label: '发布状态',
    field: 'putaway',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_MALL_CATS_STATUS, 'number'),
    },
  },
  {
    label: '邮费',
    field: 'postage',
    component: 'Input',
    // componentProps: computed(() => ({
    //   placeholder: needPostage.value ? '请输入邮费' : '不需要邮费',
    //   disabled: !needPostage.value
    // })),
  },
  {
    label: '排序',
    field: 'sort',
    component: 'Input',
  },
  // {
  //   label: '租户编号',
  //   field: 'tenantId',
  //   required: true,
  //   component: 'Input',
  // },
]

export function setNeedPostage(value:boolean){
  needPostage.value=value
}
export function getNeedPostage(){
  return needPostage.value
}
export const updateFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input',
  },
  {
    label: '商品ID',
    field: 'spuId',
    required: true,
    component: 'Input',
  },
  {
    label: '规格名称',
    field: 'name',
    required: true,
    component: 'Input',
  },
  {
    label: '规格封面',
    field: 'productImg',
    required: true,
    component: 'FileUpload',
    componentProps: {
      maxCount: 1,
      fileType: 'image',
      hintText: '图片大小不超过4M'
    },
  },
  {
    label: '原价',
    field: 'parPrice',
    required: true,
    component: 'Input',
  },
  {
    label: '所需积分',
    field: 'points',
    required: true,
    component: 'Input',
  },
  {
    label: '库存',
    field: 'stocks',
    required: true,
    component: 'Input',
  },
  {
    label: '发布状态',
    field: 'putaway',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_MALL_CATS_STATUS, 'number'),
    },
  },
  {
    label: '邮费',
    field: 'postage',
    component: 'Input',
    // componentProps: computed(() => ({
    //   placeholder: needPostage.value ? '请输入邮费' : '不需要邮费',
    //   disabled: !needPostage.value
    // })),
  },
  {
    label: '排序',
    field: 'sort',
    component: 'Input',
  },
  // {
  //   label: '租户编号',
  //   field: 'tenantId',
  //   required: true,
  //   component: 'Input',
  // },
]

