import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'
import {DICT_TYPE, getDictOptions} from '@/utils/dict'
import {getPointProductCatsPage} from "@/api/mall/pointsMall/pointsProductCategories";
import {spusUpload} from "@/api/mall/pointsMall/pointsProductManagement/spus";
import {UploadFile} from "ant-design-vue/lib/upload/interface";

// interface FileItem {
//   uid: string;
//   name?: string;
//   status?: string;
//   response?: string;
//   percent?: number;
//   url?: string;
//   preview?: string;
//   originFileObj?: any;
// }
//
// interface FileInfo {
//   file: FileItem;
//   fileList: FileItem[];
// }
//
// const fileList = ref<FileItem[]>([])
// const previewVisible = ref<boolean>(false);
// const previewImage = ref<string | undefined>('');

// const handleCancel = () => {
//   previewVisible.value = false;
// };
// const handlePreview = async (file: FileItem) => {
//   if (!file.url && !file.preview) {
//     file.preview = (await getBase64(file.originFileObj)) as string;
//   }
//   previewImage.value = file.url || file.preview;
//   previewVisible.value = true;
// };
// const handleChange = ({ fileList: newFileList }: FileInfo) => {
//   fileList.value = newFileList;
// };
// function getBase64(file: File) {
//   return new Promise((resolve, reject) => {
//     const reader = new FileReader();
//     reader.readAsDataURL(file);
//     reader.onload = () => resolve(reader.result);
//     reader.onerror = error => reject(error);
//   });
// }




export const columns: BasicColumn[] = [
  // {
  //   title: 'id',
  //   dataIndex: 'id',
  //   width: 40,
  // },
    {
        title: '分类名称',
        dataIndex: 'categoryName',
        width: 160,
    },
    {
    title: '商品名称',
    dataIndex: 'name',
    width: 160,
  },
  //   {
  //   title: '类别ID',
  //   dataIndex: 'categoryId',
  //   width: 160,
  // },
  {
    title: '封面图',
    dataIndex: 'productImg',
    width: 110,
    customRender: ({ text }) => {
      if (!text) return ''
      return h('div', {
        style: {
          height: '100px',
          width: '100px',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }
      }, [
        h('img', {
          src: text,
          style: {
            height: '100px',
            width: '100px',
            objectFit: 'cover',
            objectPosition: 'center',
          }
        })
      ])
    }

  },
  {
    title: '是否虚拟',
    dataIndex: 'isVirtual',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.POINTMALLVIRTUAL)
    },
  },
    {
    title: '邮寄类型',
    dataIndex: 'logisticsType',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.POINTMALLLOGISTICS)
    },
  },
    {
        title: '收藏量',
        dataIndex: 'collectCount',
        width: 160,
    },
    {
    title: '浏览量',
    dataIndex: 'visitCount',
    width: 160,
  },
  {
    title: '发布状态',
    dataIndex: 'state',
    width: 160,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.POINT_MALL_CATS_STATUS)
    },
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 160,
  },
  {
    title: '推荐状态',
    dataIndex: 'isRecommend',
    width: 160,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.POINT_MALL_SPU_RECOMMEND)
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    },
  },
]
const searchAutoCompleteOptions=ref([])
//用来存放选择的类别ID和名称
const searchCatIdAndNameRegister = ref({
  id:'',
  name:''
})
// pointProductSpus.data.ts

// export const getSearchCatIdAndNameRegister = () => searchCatIdAndNameRegister.value
export function getSearchCatIdAndNameRegister() {
  return searchCatIdAndNameRegister
}
export const searchFormSchema: FormSchema[] = [

  {
    label: '积分商品名称',
    field: 'name',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '类别ID',
    field: 'categoryId',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '类别名称',
    field: 'categoryName',
    component: 'AutoComplete', // 使用 AutoComplete 组件
    componentProps: {
      options: searchAutoCompleteOptions, // 可以动态绑定远程或本地数据
      showSearch: true, // 开启搜索功能
      fieldNames: {label: 'name', value: 'name'}, // 指定 label 和 value 字段
      onSearch: async (value: string) => {
        // 可以在这里处理搜索逻辑，如调用接口获取数据
        await getPointProductCatsPage({ name: value }).then(
          res=>{
            console.log(res)
            searchAutoCompleteOptions.value = res.list

          }
        )
      },
      onSelect: (value: string) => {
        // 可以在这里处理选择事件，如更新表单数据
        console.log('Selected value:', value);
        searchCatIdAndNameRegister.value = {
          id:searchAutoCompleteOptions.value.find(item => item.name === value).id,
          name:value
        }
        console.log(searchCatIdAndNameRegister.value)


      },
    },
    colProps: { span: 8 },
  },
  {
    label: '是否虚拟',
    field: 'isVirtual',
    component: 'Select',
    componentProps: {
        options: getDictOptions(DICT_TYPE.POINTMALLVIRTUAL, 'number'),
    },
    colProps: { span: 8 },
  },
  {
    label: '邮寄类型',
    field: 'logisticsType',
    component: 'Select',
    componentProps: {
        options: getDictOptions(DICT_TYPE.POINTMALLLOGISTICS, 'number'),
    },
    colProps: { span: 8 },
  },
  {
    label: '发布状态',
    field: 'state',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_MALL_CATS_STATUS, 'number'),
    },
    colProps: { span: 8 },
  },
  {
    label: '是否推荐',
    field: 'isRecommend',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_MALL_SPU_RECOMMEND, 'number'),
    },
    colProps: { span: 8 },
  },
]
//用来存放option数据
const createAutoCompleteOptions=ref([])
//用来存放选择的类别ID和名称
const createCatIdAndNameRegister = ref({
  id:'',
  name:''
})
export function getCreateCatIdAndNameRegister() {
  return createCatIdAndNameRegister
}





export const createFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input',
  },
  {
    label: '积分商品名称',
    field: 'name',
    required: true,
    component: 'Input',
  },
  // {
  //   label: '类别ID',
  //   field: 'categoryId',
  //   required: true,
  //   component: 'Input',
  //
  // },
  {
    label: '类别名称',
    field: 'categoryName',
    required: true,
    component: 'AutoComplete', // 使用 AutoComplete 组件
    componentProps: {
      options: createAutoCompleteOptions, // 可以动态绑定远程或本地数据
      showSearch: true, // 开启搜索功能
      fieldNames: {label: 'name', value: 'name'}, // 指定 label 和 value 字段
      onSearch: async (value: string) => {
        // 可以在这里处理搜索逻辑，如调用接口获取数据
        await getPointProductCatsPage({ name: value }).then(
          res=>{
            // console.log(res)
            createAutoCompleteOptions.value = res.list

          }
        )
      },
      onSelect: (value: string) => {
        // 可以在这里处理选择事件，如更新表单数据
        console.log('Selected value:', value);
        createCatIdAndNameRegister.value = {
          id:createAutoCompleteOptions.value.find(item => item.name === value).id,
          name:value
        }
        console.log(createCatIdAndNameRegister.value)


      },
    },
  },
  // {
  //   label: '图片',
  //   field: 'productImg',
  //   required: true,
  //   component: 'ImageUpload',
  //   componentProps: {
  //     api: async (option) => {
  //       const { file } = option;
  //       console.log(file)
  //       // const formData = new FormData();
  //       // formData.append('file', file);
  //       const request = {
  //         file: file, // 直接传递文件对象
  //
  //       };
  //       // 调用上传接口
  //       const res = await spusUpload(request);
  //       // 返回图片URL
  //       return res.data;
  //     },
  //     listType: 'picture-card',
  //     multiple: false,
  //     maxCount: 1,
  //   }
  // },
  {
    label: '图片',
    field: 'productImg',
    required: true,
    component: 'FileUpload',
    componentProps: {
      maxCount: 5,
      fileType: 'image',
        hintText: '图片大小不超过4M，第一张默认为封面图'
    },
  },

  {
    label: '是否虚拟',
    field: 'isVirtual',
    required: true,
    component: 'Select',
    componentProps: {
        options: getDictOptions(DICT_TYPE.POINTMALLVIRTUAL, 'number'),
    },
  },
  {
    label: '邮寄类型',
    field: 'logisticsType',
    required: true,
    component: 'Select',
    componentProps: {
        options: getDictOptions(DICT_TYPE.POINTMALLLOGISTICS, 'number'),
    },
  },
  {
    label: '产品简介',
    field: 'productIntro',
    component: 'Input',
  },
  {
    label: '产品详情',
    field: 'productDetail',
    component: 'Editor',
  },
  {
    label: '分享图片',
    field: 'shareImg',
    component: 'FileUpload',
    componentProps: {
      maxCount: 1,
      fileType: 'image',
      hintText: '图片大小不超过4M，若没有选择默认为封面图'
    }
  },
  {
    label: '分享标题',
    field: 'shareTitle',
    component: 'Input',
  },
  {
    label: '分享内容',
    field: 'shareContent',
    component: 'InputTextArea',
  },
  {
    label: '发布状态',
    field: 'state',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_MALL_CATS_STATUS, 'number'),
    },
  },
  {
    label: '排序',
    field: 'sort',
    required: true,
    component: 'Input',
  },
  {
    label: '是否推荐',
    field: 'isRecommend',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_MALL_SPU_RECOMMEND, 'number'),
    },
  },
  {
    label: '推荐时间',
    field: 'recommendTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'x',
    },
  },
  // {
  //   label: '租户编号',
  //   field: 'tenantId',
  //   required: true,
  //   component: 'Input',
  // },
]

const updateAutoCompleteOptions=ref([])
const updateCatIdAndNameRegister = ref({
  id:'',
  name:''
})
export function getUpdateCatIdAndNameRegister() {
  return updateCatIdAndNameRegister
}

export const updateFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input',
  },
  {
    label: '积分商品名称',
    field: 'name',
    required: true,
    component: 'Input',
  },
  {
    label: '类别ID',
    field: 'categoryId',
    required: false,
    component: 'Input',
    ifShow:false
  },
  {
    label: '类别名称',
    field: 'categoryName',
    required: true,
      component: 'AutoComplete', // 使用 AutoComplete 组件
      componentProps: {
          options: updateAutoCompleteOptions, // 可以动态绑定远程或本地数据
          showSearch: true, // 开启搜索功能
          fieldNames: {label: 'name', value: 'name'}, // 指定 label 和 value 字段
          onSearch: async (value: string) => {
              // 可以在这里处理搜索逻辑，如调用接口获取数据
              await getPointProductCatsPage({ name: value }).then(
                  res=>{
                      console.log(res)
                      updateAutoCompleteOptions.value = res.list

                  }
              )
          },
          onSelect: (value: string) => {
              // 可以在这里处理选择事件，如更新表单数据
              console.log('Selected value:', value);
              updateCatIdAndNameRegister.value = {
                  id:updateAutoCompleteOptions.value.find(item => item.name === value).id,
                  name:value
              }
              console.log(updateCatIdAndNameRegister.value)


          },
      },
  },
  {
    label: '图片',
    field: 'productImg',
    required: true,
      component: 'FileUpload',
      componentProps: {
          maxCount: 5,
          fileType: 'image',
          hintText: '图片大小不超过4M，第一张默认为封面图'
      },
  },
  {
    label: '是否虚拟',
    field: 'isVirtual',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINTMALLVIRTUAL, 'number'),
    },
  },
  {
    label: '邮寄类型',
    field: 'logisticsType',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINTMALLLOGISTICS, 'number'),
    },
  },
  {
    label: '产品简介',
    field: 'productIntro',
    component: 'Input',
  },
  {
    label: '产品详情',
    field: 'productDetail',
    component: 'Editor',
  },
  {
    label: '分享图片',
    field: 'shareImg',
    component: 'FileUpload',
    componentProps: {
      maxCount: 1,
      fileType: 'image',
      hintText: '图片大小不超过4M，若没有选择默认为封面图'
    }
  },
  {
    label: '分享标题',
    field: 'shareTitle',
    component: 'Input',
  },
  {
    label: '分享内容',
    field: 'shareContent',
    component: 'InputTextArea',
  },
  {
    label: '发布状态',
    field: 'state',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_MALL_CATS_STATUS, 'number'),
    },
  },
  {
    label: '排序',
    field: 'sort',
    required: true,
    component: 'Input',
  },
  {
    label: '是否推荐',
    field: 'isRecommend',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_MALL_SPU_RECOMMEND, 'number'),
    },
  },
  {
    label: '推荐时间',
    field: 'recommendTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'x',
    },
  },
  // {
  //   label: '租户编号',
  //   field: 'tenantId',
  //   required: true,
  //   component: 'Input',
  // },
  // {
  //   label: '是否删除',
  //   field: 'deleted',
  //   required: true,
  //   component: 'RadioButtonGroup',
  //   componentProps: {
  //     options:[],
  //   },
]
