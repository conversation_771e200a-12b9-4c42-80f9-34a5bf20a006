<script lang="ts" setup>
import PointProductSpusModal from './PointProductSpusModal.vue'
import { columns, searchFormSchema } from './pointProductSpus.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import {
    deletePointProductSpus,
    exportPointProductSpus,
    getPointProductSpusPage,
    updatePointProductSpus
} from '@/api/mall/pointsMall/pointsProductManagement/spus'
import PointProductSkus from '@/views/mall/pointsMall/pointsProductManagement/pointProductSkus/index.vue'
defineOptions({ name: 'PointProductSpus' })


//sku弹窗相关
const visible = ref(false)
const handleOk = (e: MouseEvent) => {
    // console.log(e);
    visible.value = false;
  currentSpuId.value = null
};
//存储spu信息
const spuForSku=ref()
const currentSpuId = ref<number | null>(null)

// 创建模板引用
// const pointProductSkusRef = ref()



const { t } = useI18n()
const { createConfirm, createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
let originalTableData=[]
const [registerTable, { getForm, reload }] = useTable({
  title: '商品信息列表',
  api: async (params) => {
  const res = await getPointProductSpusPage(params);
  // console.log(res, 'res');
  //     console.log("原始数据");
  //     修改前寄存原始数据
      originalTableData = JSON.parse(JSON.stringify(res.list));
      // console.log(originalTableData)

      res.list.forEach(item=>{
      item.productImg=item.productImg[0]
  })


  // 对数据按 sort 从小到大排序
  // return res.list.sort((a, b) => a.sort - b.sort);
    return res.list
  },
    columns,
    formConfig: { labelWidth: 120, schemas: searchFormSchema },
    useSearchForm: true,
    showTableSetting: true,
    actionColumn: {
      width: 300,
      title: t('common.action'),
      dataIndex: 'action',
      fixed: 'right',
    },
})

function handleCreate() {
  openModal(true, { isUpdate: false })
}

function handleEdit(record: Recordable) {
  openModal(true, { record, isUpdate: true })
    // console.log(record)
}

async function handleExport() {
  createConfirm({
    title: t('common.exportTitle'),
    iconType: 'warning',
    content: t('common.exportMessage'),
    async onOk() {
      await exportPointProductSpus(getForm().getFieldsValue())
      createMessage.success(t('common.exportSuccessText'))
    },
  })
}

async function handleDelete(record: Recordable) {
  await deletePointProductSpus(record.id)
  createMessage.success(t('common.delSuccessText'))
  reload()
}
//规格详情
async function handleSkus(record: Recordable){

     spuForSku.value=originalTableData.find(item=>item.id===record.id)
     await setSpuForSku(spuForSku.value)
      currentSpuId.value = record.id // 设置当前 SPU ID
     visible.value=  true
}


// const handleModalOpenChange = (open: boolean) => {
//   if (open && pointProductSkusRef.value) {
//     // 调用子组件的 reload 方法
//     pointProductSkusRef.value.reload?.()
//   }
// }




async function handleStatus(record: Recordable) {
    // console.log(originalTableData.value)
    let originalRecord = originalTableData.find(item=>item.id===record.id)

    originalRecord.state=record.state == 0 ? 1 : 0
    // record.state = record.state == 0 ? 1 : 0
    //为了展示封面图，productImg做了修改，不能传入
    // delete record.productImg

    await updatePointProductSpus(originalRecord).then(res=>{
      if(res){
        createMessage.success(record.state == 0 ?"取消发布成功":"发布成功")
      }

    })

    reload()
}

async function handleRecommend(record: Recordable) {
    let originalRecord = originalTableData.find(item=>item.id===record.id)

    originalRecord.isRecommend = record.isRecommend == 0 ? 1 : 0

    await updatePointProductSpus(originalRecord).then(res=>{
      if(res){
        createMessage.success(record.isRecommend == 0 ?"取消推荐成功":"推荐成功")

      }

    })
    reload()
}


</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" v-auth="['pointMall:point-product-spus:create']" :preIcon="IconEnum.ADD" @click="handleCreate">
          {{ t('action.create') }}
        </a-button>
        <a-button v-auth="['pointMall:point-product-spus:export']" :preIcon="IconEnum.EXPORT" @click="handleExport">
          {{ t('action.export') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              { icon: IconEnum.EDIT, label: t('action.edit'), auth: 'pointMall:point-product-spus:update', onClick: handleEdit.bind(null, record) },
              {
                icon: IconEnum.DELETE,
                danger: true,
                label: t('action.delete'),
                auth: 'pointMall:point-product-spus:delete',
                popConfirm: {
                  title: t('common.delMessage'),
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
              {
                  label:'规格管理',
                  auth:'pointMall:point-product-spus:update',
                  onClick: handleSkus.bind(null, record)
              },
              {
                  label:'发布',
                  ifShow: record.state === 1,
                  auth:'pointMall:point-product-spus:update',
                  onClick: handleStatus.bind(null, record)
              },
              {
                  label:'取消发布',
                  danger: true,
                  ifShow: record.state === 0,
                  auth:'pointMall:point-product-spus:update',
                  popConfirm: {
                  title: '确认要取消发布吗',
                  placement: 'left',
                  confirm: handleStatus.bind(null, record),
                },
              },
              {
                  label:'推荐',

                  ifShow: record.isRecommend === 1,
                  auth:'pointMall:point-product-spus:update',
                  onClick: handleRecommend.bind(null, record)
              },
              {
                  label:'取消推荐',
                  danger: true,
                  ifShow: record.isRecommend === 0,
                  auth:'pointMall:point-product-spus:update',
                  popConfirm: {
                  title: '确认要取消推荐吗',
                  placement: 'left',
                  confirm: handleRecommend.bind(null, record),
                },
              }
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PointProductSpusModal @register="registerModal" @success="reload()" />
      <a-modal
          v-model:visible="visible"
          title="规格管理"
          @ok="handleOk"
          @cancel="currentSpuId = null"
          width="100%">
          <point-product-skus :spu-id="currentSpuId"/>
      </a-modal>
  </div>
</template>
<script lang="ts">
let spuForSku = ref()

export function getSpuForSku() {
  return spuForSku.value
}

export function setSpuForSku(data: any) {
  spuForSku.value = data
}

</script>
