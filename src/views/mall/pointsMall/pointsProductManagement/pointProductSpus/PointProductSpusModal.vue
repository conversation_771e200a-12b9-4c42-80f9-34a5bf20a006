<script lang="ts" setup>
import { ref, unref } from 'vue'
import {
    createFormSchema, getCreateCatIdAndNameRegister, getSearchCatIdAndNameRegister, getUpdateCatIdAndNameRegister,

    updateFormSchema
} from './pointProductSpus.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { BasicForm, useForm } from '@/components/Form'
import { BasicModal, useModalInner } from '@/components/Modal'
import { createPointProductSpus, getPointProductSpus, updatePointProductSpus } from '@/api/mall/pointsMall/pointsProductManagement/spus'

defineOptions({ name: 'PointProductSpusModal' })

const emit = defineEmits(['success', 'register'])

const { t } = useI18n()
const { createMessage } = useMessage()
const isUpdate = ref(true)

const [registerForm, { setFieldsValue, resetFields, resetSchema, validate }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: createFormSchema,
  showActionButtonGroup: false,
  actionColOptions: { span: 23 },
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields()
  setModalProps({
    confirmLoading: false ,
    bodyStyle: {
      maxHeight: '600px',
      overflowY: 'auto',
      marginTop:"20px",
      marginBottom:"20px",
      marginRight: "10px",
    }



  })
  isUpdate.value = !!data?.isUpdate
  if (unref(isUpdate)) {
    resetSchema(updateFormSchema)
    const res = await getPointProductSpus(data.record.id)
    console.log(res)
    setFieldsValue({ ...res })
  }
})

async function handleSubmit() {
  try {
    const values = await validate()
    console.log(values)
    
    // 将 productImg 字符串转换为数组
    if (values.productImg && typeof values.productImg === 'string') {
      values.productImg = values.productImg.split(',')
    }
    
    setModalProps({ confirmLoading: true })
    if (unref(isUpdate)) {
      console.log(values)


        const cat = getUpdateCatIdAndNameRegister()
        // values.categoryName = cat.value.name
      //等于空说明没有修改，直接传原值
      //||cat.value.name===values.categoryName
      console.log("传过来的值是：")
      console.log(cat.value)

      if(cat.value.id!==""){
        values.categoryId = cat.value.id
      }







        // values.productImg=["1"]
        // values.deleted=false
        // values.tenantId="1"
      if (values.productImg && typeof values.productImg === 'string') {
        values.productImg = values.productImg.split(',');
      }



        await updatePointProductSpus(values)
    }
    else {

        const cat=getCreateCatIdAndNameRegister()
        values.categoryName=cat.value.name
        values.categoryId=cat.value.id


      // console.log("传过来的值是：")
      // console.log(cat.value)

      // values.productImg=["1"]
      // values.deleted=false
      // values.tenantId="1"
      if (values.productImg && typeof values.productImg === 'string') {
        values.productImg = values.productImg.split(',');
      }


      if(values.shareImg===''||values.shareImg===null||values.shareImg===undefined){
        values.shareImg=values.productImg[0]
      }
      console.log("上传前的值")
      console.log(values)



      await createPointProductSpus(values)
    }

    closeModal()
    emit('success')
    createMessage.success(t('common.saveSuccessText'))
  } finally {
    setModalProps({ confirmLoading: false })
  }
}
</script>
<template>
  <BasicModal v-bind="$attrs" :title="isUpdate ? t('action.edit') : t('action.create')" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
