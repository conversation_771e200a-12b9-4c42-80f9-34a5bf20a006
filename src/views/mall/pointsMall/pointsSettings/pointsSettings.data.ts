import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

export const columns: BasicColumn[] = [
  {
    title: '积分类型',
    dataIndex: 'pointType',
    width: 120,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.MEMBER_POINT_BIZ_TYPE)
    },
  },
  { title: '积分名称', dataIndex: 'pointName', width: 180 },
  { title: '每次可得积分', dataIndex: 'point', width: 100 },
  { title: '获得次数', dataIndex: 'limitTimes', width: 100 },
  { title: '最大积分', dataIndex: 'pointLimit', width: 100 },
  { title: '操作时间', dataIndex: 'updateTime', width: 160 },
]
// member_point_biz_type
export const searchFormSchema: FormSchema[] = [
  { label: '积分名称', field: 'pointName', component: 'Input', colProps: { span: 8 } },
]

export const formSchema: FormSchema[] = [
  { label: 'ID', field: 'id', component: 'Input', show: false },

  {
    label: '积分类型',
    field: 'pointType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.MEMBER_POINT_BIZ_TYPE),
    },
  },
  { label: '积分名称', field: 'pointName', component: 'Input' },
  { label: '每次可得积分', field: 'point', component: 'InputNumber' },
  { label: '获得次数', field: 'limitTimes', component: 'InputNumber' },
  { label: '最大积分', field: 'pointLimit', component: 'InputNumber' },
]
