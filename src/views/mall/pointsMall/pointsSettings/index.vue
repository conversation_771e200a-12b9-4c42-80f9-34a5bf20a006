<script lang="ts" setup>
import { columns, searchFormSchema } from './pointsSettings.data'
import PointsRuleForm from './PointsRuleForm.vue'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { getPointConfigPage } from '@/api/mall/pointsMall/pointsSettings'

defineOptions({ name: 'PointsSettings' })
const [registerModal, { openModal }] = useModal()
const [registerTable] = useTable({
  title: '积分规则',
  api: getPointConfigPage,
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  showIndexColumn: false,
  rowKey: 'id', // 指定行的唯一键
  actionColumn: {
    width: 80,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
})
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" :pre-icon="IconEnum.ADD" @click="openModal"> 新增 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'point'">
          <span style="color:red">{{ record.point }}</span>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: IconEnum.EYE,
              label: '详情',
              onClick: () => openModal(true, { ...record }),
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <PointsRuleForm @register="registerModal" />
  </div>
</template>
