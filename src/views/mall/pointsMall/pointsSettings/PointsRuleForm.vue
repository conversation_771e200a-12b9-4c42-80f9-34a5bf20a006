<script lang="ts" setup>
import { nextTick, ref, unref } from 'vue'
import { formSchema } from './pointsSettings.data'
import { BasicForm, useForm } from '@/components/Form'
import { BasicModal, useModalInner } from '@/components/Modal'
import { useMessage } from '@/hooks/web/useMessage'
import { createPointConfig, getPointConfigDetail, updatePointConfig } from '@/api/mall/pointsMall/pointsSettings'

const emit = defineEmits(['success', 'register'])
const { createMessage } = useMessage()
const isUpdate = ref(false)
const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: formSchema,
  showActionButtonGroup: false,
  actionColOptions: { span: 23 },
})
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields()
  setModalProps({ confirmLoading: false })
  isUpdate.value = !!data?.id
  if (unref(isUpdate)) {
    const res = await getPointConfigDetail({ id: data.id })
    setFieldsValue({ ...res.data })
    await nextTick()
  }
})

async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true })
    if (values.id)
      await updatePointConfig(values)
    else
      await createPointConfig(values)
    console.log("到这里了吗")
    closeModal()
    console.log("好像没有")
    emit('success')
    createMessage.success('操作成功')
  }
  finally {
    setModalProps({ confirmLoading: false })
  }
}
</script>
<template>
  <BasicModal v-bind="$attrs" :title="isUpdate ? '编辑积分配置' : '新增积分配置'" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
