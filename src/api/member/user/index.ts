import { defHttp } from '@/utils/http/axios'

export interface UserVO {
  id: number
  mobile: string
  password: string
  status: number
  registerIp: string
  loginIp: string
  loginDate: Date
  nickname: string
  avatar: string
  name: string
  sex: number
  areaId: number
  birthday: Date
  mark: string
  createTime: Date
}
interface UserPageParams {
  pageNo: number
  pageSize: number
  status: number
}
export const getWXUserPage = (params: UserPageParams) => {
  return defHttp.get({ url: '/member/user/page', params })
}
// https://basic.lingmaoshuke.com/api/admin-api/member/user/page?pageNo=1&pageSize=10&status=2
// https://basic.lingmaoshuke.com/api/admin-api/member/user//confirm-log-off

export const confirmLogoff = (id: string) => {
  return defHttp.put({ url: `/member/user/confirm-log-off?id=${id}` })
}
// 查询会员用户列表
export function getUserPage(params) {
  return defHttp.get({ url: '/member/user/page', params })
}
// 查询会员用户列表options
export function getUserAllSimpleList(params) {
  return defHttp.get({ url: '/member/user/list-all-simple', params: { ...params, keyword: params.keyword || '' } })
}
// 查询会员用户详情
export function getUser(id: number) {
  return defHttp.get({ url: `/member/user/get?id=${id}` })
}

// 修改会员用户
export function updateUser(data: UserVO) {
  return defHttp.put({ url: '/member/user/update', data })
}

// 修改会员用户等级
export function updateUserLevel(data: any) {
  return defHttp.put({ url: '/member/user/update-level', data })
}

// 获得会员用户分页
export function getUserList(params) {
  return defHttp.get({ url: '/member/user/page', params })
}

// 获取会员精简信息列表
export function getUserListAllSimple(params) {
  return defHttp.get({ url: '/member/user/list-all-simple', params })
}

// 获得会员车辆信息
export function getUserCar(params) {
  return defHttp.get({ url: '/club/member/cars/get-by-member', params })
}

// 更新用户信息
export function updateUserInfo(data) {
  return defHttp.put({ url: `/member/user/update`, data })
}

// 认证审核
export function authAudit(data) {
  return defHttp.put({ url: `/member/user/auth/audit`, data })
}