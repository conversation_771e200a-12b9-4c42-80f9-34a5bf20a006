import { defHttp } from '@/utils/http/axios'

// page
export function getBizOwnerPage(params) {
  return defHttp.get({ url: '/platform/admin/biz-owner/page', params })
}

// detail
export function getBizOwnerDetail(params) {
  return defHttp.get({ url: '/platform/admin/biz-owner/get', params })
}

// 更改状态
export function updateBizOwnerStatus(params) {
  return defHttp.put({ url: '/platform/admin/biz-owner/update-status', params })
}
