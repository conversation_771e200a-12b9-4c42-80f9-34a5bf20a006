import { defHttp } from '@/utils/http/axios'

// 查询俱乐部标签列表
export function getTagPage(params) {
  return defHttp.get({ url: '/club/tag/page', params })
}

// 查询俱乐部标签详情
export function getTag(id: number) {
  return defHttp.get({ url: `/club/tag/get?id=${id}` })
}

// 新增俱乐部标签
export function createTag(data) {
  return defHttp.post({ url: '/club/tag/create', data })
}

// 修改俱乐部标签
export function updateTag(data) {
  return defHttp.put({ url: '/club/tag/update', data })
}

// 删除俱乐部标签
export function deleteTag(id: number) {
  return defHttp.delete({ url: `/club/tag/delete?id=${id}` })
}

// 导出俱乐部标签 Excel
export function exportTag(params) {
  return defHttp.download({ url: '/club/tag/export-excel', params }, '俱乐部标签.xls')
}