import { defHttp } from '@/utils/http/axios'

// 点赞记录
export function getPraisePage(params) {
  return defHttp.get({ url: '/club/member-like/page', params })
}

// 积分记录
export function getScorePage(params) {
  return defHttp.get({ url: '/member/point/record/page', params })
}


export function createScoreRecord(data) {
  return defHttp.post({ url: '/member/point/record/create', data })
}

// 新增
export function getScoreDetail(params) {
  return defHttp.get({ url: `/member/point/record/get`, params })
}

// 会员浏览记录
export function getProductHistoryPage(params) {
  return defHttp.get({ url: '/product/browse-history/page', params })
}

// 添加积分
export function createPointRecord(params) {
  return defHttp.post({ url: '/member/point/record/create', params })
}
